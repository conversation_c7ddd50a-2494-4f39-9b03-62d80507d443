.PHONY = all
GOOS ?= linux
GOARCH ?= amd64
VERSION ?= latest
COMMIT = $(shell git log --format="%h" -n 1|tr -d '\n')
TIMESTAMP = $(shell date -u "+%Y-%m-%dT%H:%M:%SZ")

hero-apiserver:
	@echo "Compiling hero-apiserver source for $(GOOS) $(GOARCH)"
	@CGO_ENABLED=0 GOOS=$(GOOS) GOARCH=$(GOARCH) go build -o bin/hero-apiserver -ldflags "-s -w -X main.version=$(VERSION)-$(COMMIT)-$(TIMESTAMP)" cmd/server/main.go

build: hero-apiserver
	@echo $(VERSION)
	@echo "hero-apiserver built successfully"

clean:
	@rm -f bin/hero-apiserver
	@echo "remove hero-apiserver"

image: build
	@echo "Build Docker Image"
	@cd build && sh build.sh $(VERSION)

push: image
	@echo "Pushing image version"
	@docker push registry.bitahub.com:5000/leinaoyun-tag/hero-apiserver:$(VERSION)

install:
	@echo "Deploy to Container Platform"
	@kubectl  apply -f deploy.yaml

uninstall:
	@echo "Deploy to Kun Container Platform"
	@kubectl  delete -f deploy.yaml
test:
	@echo $(VERSION)
	go test ./... -v