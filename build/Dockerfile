FROM registry.cnbita.com:5000/golangci/golang:1.20 as builder
ARG TARGETOS
ARG TARGETARCH

WORKDIR /workspace
COPY go.mod go.mod
COPY go.sum go.sum
RUN go env -w GOPROXY=https://goproxy.cn,direct
RUN go mod download

COPY cmd/ cmd/
COPY internal/ internal/
COPY log/ log/

RUN CGO_ENABLED=0 GOOS=${TARGETOS:-linux} GOARCH=${TARGETARCH} go build -a -o hero-apiserver cmd/server/main.go

FROM registry.cnbita.com:5000/golangci/alpine:3.14
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
WORKDIR /
COPY --from=builder /workspace/hero-apiserver .

CMD ["/hero-apiserver"]
