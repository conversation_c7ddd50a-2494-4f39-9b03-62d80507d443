module hero-apiserver

go 1.18

require (
	github.com/asaskevich/govalidator v0.0.0-20200108200545-475eaeb16496
	github.com/emicklei/go-restful v2.16.0+incompatible
	github.com/pkg/errors v0.9.1
	github.com/prometheus/client_golang v1.16.0
	github.com/prometheus/common v0.44.0
	github.com/sirupsen/logrus v1.9.0
	github.com/spf13/cobra v1.7.0
	github.com/spf13/viper v1.18.2
	gitlab.bitahub.com/hero-os/hero-os-util v1.0.2
	gopkg.in/yaml.v2 v2.4.0
	k8s.io/api v0.28.2
	k8s.io/apimachinery v0.28.2
	k8s.io/klog v1.0.0
)

require (
	github.com/apache/rocketmq-client-go/v2 v2.1.2
	github.com/go-resty/resty/v2 v2.16.2
	github.com/minio/minio-go/v7 v7.0.45
	github.com/stretchr/testify v1.8.4
	golang.org/x/crypto v0.25.0
	golang.org/x/exp v0.0.0-20230905200255-921286631fa9
	k8s.io/client-go v12.0.0+incompatible
	volcano.sh/apis v1.7.0
	volcano.sh/volcano v1.7.0
)

require (
	github.com/aliyun/alibaba-cloud-sdk-go v1.61.18 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/blang/semver/v4 v4.0.0 // indirect
	github.com/buger/jsonparser v1.1.1 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/emicklei/go-restful/v3 v3.9.0 // indirect
	github.com/emirpasic/gods v1.12.0 // indirect
	github.com/evanphx/json-patch v4.12.0+incompatible // indirect
	github.com/fsnotify/fsnotify v1.7.0 // indirect
	github.com/go-errors/errors v1.0.1 // indirect
	github.com/go-logr/logr v1.2.4 // indirect
	github.com/go-openapi/jsonpointer v0.19.6 // indirect
	github.com/go-openapi/jsonreference v0.20.1 // indirect
	github.com/go-openapi/swag v0.22.3 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da // indirect
	github.com/golang/mock v1.6.0 // indirect
	github.com/golang/protobuf v1.5.3 // indirect
	github.com/google/cadvisor v0.46.0 // indirect
	github.com/google/gnostic v0.5.7-v3refs // indirect
	github.com/google/go-cmp v0.6.0 // indirect
	github.com/google/gofuzz v1.2.0 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/imdario/mergo v0.3.9 // indirect
	github.com/inconshreveable/mousetrap v1.1.0 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/klauspost/compress v1.17.6 // indirect
	github.com/klauspost/cpuid/v2 v2.2.6 // indirect
	github.com/magiconair/properties v1.8.7 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.4 // indirect
	github.com/minio/md5-simd v1.1.2 // indirect
	github.com/minio/sha256-simd v1.0.1 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/moby/sys/mountinfo v0.6.2 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/munnerz/goautoneg v0.0.0-20191010083416-a7dc8b61c822 // indirect
	github.com/nacos-group/nacos-sdk-go v1.1.4 // indirect
	github.com/opencontainers/selinux v1.10.0 // indirect
	github.com/patrickmn/go-cache v2.1.0+incompatible // indirect
	github.com/pelletier/go-toml/v2 v2.1.0 // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/prometheus/client_model v0.4.0 // indirect
	github.com/prometheus/procfs v0.10.1 // indirect
	github.com/rs/xid v1.5.0 // indirect
	github.com/sagikazarmark/locafero v0.4.0 // indirect
	github.com/sagikazarmark/slog-shim v0.1.0 // indirect
	github.com/sourcegraph/conc v0.3.0 // indirect
	github.com/spf13/afero v1.11.0 // indirect
	github.com/spf13/cast v1.6.0 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/stretchr/objx v0.5.0 // indirect
	github.com/subosito/gotenv v1.6.0 // indirect
	github.com/tidwall/gjson v1.13.0 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.0 // indirect
	go.uber.org/atomic v1.9.0 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	go.uber.org/zap v1.26.0 // indirect
	golang.org/x/net v0.27.0 // indirect
	golang.org/x/oauth2 v0.15.0 // indirect
	golang.org/x/sync v0.7.0 // indirect
	golang.org/x/sys v0.22.0 // indirect
	golang.org/x/term v0.22.0 // indirect
	golang.org/x/text v0.16.0 // indirect
	golang.org/x/time v0.6.0 // indirect
	google.golang.org/appengine v1.6.7 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20231120223509-83a465c0220f // indirect
	google.golang.org/grpc v1.59.0 // indirect
	google.golang.org/protobuf v1.31.0 // indirect
	gopkg.in/inf.v0 v0.9.1 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.0.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	k8s.io/apiserver v0.26.0 // indirect
	k8s.io/cloud-provider v0.25.0 // indirect
	k8s.io/component-base v0.26.0 // indirect
	k8s.io/component-helpers v0.26.0 // indirect
	k8s.io/csi-translation-lib v0.25.0 // indirect
	k8s.io/klog/v2 v2.100.1 // indirect
	k8s.io/kube-openapi v0.0.0-20220803162953-67bda5d908f1 // indirect
	k8s.io/kube-scheduler v0.0.0 // indirect
	k8s.io/kubernetes v1.25.0 // indirect
	k8s.io/mount-utils v0.25.0 // indirect
	k8s.io/utils v0.0.0-20230406110748-d93618cff8a2 // indirect
	sigs.k8s.io/json v0.0.0-20221116044647-bc3834ca7abd // indirect
	sigs.k8s.io/structured-merge-diff/v4 v4.2.3 // indirect
	sigs.k8s.io/yaml v1.3.0 // indirect
	stathat.com/c/consistent v1.0.0 // indirect
)

replace k8s.io/api => k8s.io/api v0.25.0

replace k8s.io/apiextensions-apiserver => k8s.io/apiextensions-apiserver v0.25.0

replace k8s.io/apimachinery => k8s.io/apimachinery v0.26.0-alpha.0

replace k8s.io/apiserver => k8s.io/apiserver v0.25.0

replace k8s.io/cli-runtime => k8s.io/cli-runtime v0.25.0

replace k8s.io/client-go => k8s.io/client-go v0.25.0

replace k8s.io/cloud-provider => k8s.io/cloud-provider v0.25.0

replace k8s.io/cluster-bootstrap => k8s.io/cluster-bootstrap v0.25.0

replace k8s.io/code-generator => k8s.io/code-generator v0.25.1-rc.0

replace k8s.io/component-base => k8s.io/component-base v0.25.0

replace k8s.io/component-helpers => k8s.io/component-helpers v0.25.0

replace k8s.io/controller-manager => k8s.io/controller-manager v0.25.0

replace k8s.io/cri-api => k8s.io/cri-api v0.25.1-rc.0

replace k8s.io/csi-translation-lib => k8s.io/csi-translation-lib v0.25.0

replace k8s.io/kube-aggregator => k8s.io/kube-aggregator v0.25.0

replace k8s.io/kube-controller-manager => k8s.io/kube-controller-manager v0.25.0

replace k8s.io/kube-proxy => k8s.io/kube-proxy v0.25.0

replace k8s.io/kube-scheduler => k8s.io/kube-scheduler v0.25.0

replace k8s.io/kubectl => k8s.io/kubectl v0.25.0

replace k8s.io/kubelet => k8s.io/kubelet v0.25.0

replace k8s.io/legacy-cloud-providers => k8s.io/legacy-cloud-providers v0.25.0

replace k8s.io/metrics => k8s.io/metrics v0.25.0

replace k8s.io/mount-utils => k8s.io/mount-utils v0.25.3-rc.0

replace k8s.io/pod-security-admission => k8s.io/pod-security-admission v0.25.0

replace k8s.io/sample-apiserver => k8s.io/sample-apiserver v0.25.0

replace k8s.io/sample-cli-plugin => k8s.io/sample-cli-plugin v0.25.0

replace k8s.io/sample-controller => k8s.io/sample-controller v0.25.0
