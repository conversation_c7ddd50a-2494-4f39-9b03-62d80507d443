package log

import (
	"fmt"
	"os"
	"strings"

	"github.com/sirupsen/logrus"
)

type Formatter struct {
}

var (
	logType string
)

func (f *Formatter) Format(entry *logrus.Entry) ([]byte, error) {

	data := []string{}

	for k, v := range entry.Data {

		if k == "sessionID" {
			continue
		}

		data = append(data, fmt.Sprintf("\"%s\":%v", k, v))
	}
	fields := strings.Join(data, " ")

	return []byte(fmt.Sprintf("{\"time\":\"%s\", \"log_level\":\"%s\", \"log_type\":\"%s\", \"caller\":\"%s:%d\",  \"with_fields\":\"%s\", \"message\":\"%s\"}\n", entry.Time.Format("2006-01-02 15:04:05.999"), entry.Level, logType, entry.Caller.File, entry.Caller.Line, strings.ReplaceAll(fields, "\"", ""), strings.ReplaceAll(entry.Message, "\"", ""))), nil
}

func InitLog(s string) {
	logType = s
	logrus.SetReportCaller(true)
	logrus.SetFormatter(new(Formatter))
	logrus.SetOutput(os.Stdout)

	// 设置日志级别，只打印错误日志
	//logrus.SetLevel(logrus.ErrorLevel)
}
