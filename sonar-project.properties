sonar.sourceEncoding=UTF-8
sonar.sources=.
sonar.language=go
sonar.inclusions=**/internal/uils**,**/internal/common**,**/internal/monitor**,**/internal/lokiclient**,**/sonar/**
sonar.test.inclusions=**/*_test.go
sonar.test.exclusions=**/vendor/**
sonar.go.tests.reportPaths=sonar/reports/test-report.out
sonar.go.coverage.reportPaths=sonar/reports/coverage.out
sonar.go.golangci-lint.reportPaths=sonar/reports/report.xml
sonar.qualitygate.wait=true