apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  name: {{ .Values.apiservice.name}}
spec:
  group: {{ .Values.apiservice.group}}
  groupPriorityMinimum: {{ .Values.apiservice.groupPriorityMinimum}}
  versionPriority: {{ .Values.apiservice.versionPriority}}
  version: {{ .Values.apiservice.version}}
  insecureSkipTLSVerify: true
  service:
    name: {{ include "resource-manager.fullname" . }}
    namespace: {{ .Values.namespace}} 
    port: {{ .Values.service.port }}