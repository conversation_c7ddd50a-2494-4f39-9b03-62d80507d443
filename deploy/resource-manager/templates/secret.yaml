apiVersion: v1
data:
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURZekNDQWt1Z0F3SUJBZ0lDQm5vd0RRWUpLb1pJaHZjTkFRRUxCUUF3RnpFVk1CTUdBMVVFQ2hNTVlYVjAKYUMxaFpHRndkR1Z5TUNBWERUSXpNVEF3TnpBMk1EWXlOVm9ZRHpJeE1qTXhNREEzTURZd05qSTFXakFSTVE4dwpEUVlEVlFRS0V3WlRSVkpXUlZJd2dnRWlNQTBHQ1NxR1NJYjNEUUVCQVFVQUE0SUJEd0F3Z2dFS0FvSUJBUUM1ClJCWlRKNTRZNjU0c21hclJoRVVMOGhEVUpHMmR0VFU0WmtTdk5TaTdrMk1kRUtzRGJNd3JJNUdOTjluQjFJU3YKYWsvOWVHRUwvR3FrakNsN2dyVWJocVQxcWhNTUplSVdpcmFvQTMvSnlRMkFZVGwwUlFKOVdqMW1PbVM0STgzSgpFdnBZbTZFYWRMSUljNS9HaDkxNFlLZTB6bVorNUk4bW1kNWp6SndoV1ZJeWV4a2dJRWZUejE0TU8rTVRtdVlJCnZ0emhMTzFUcEx6U09sUjRWN2pnemlMM2ZHRDgxWEprSlB1RHNCcGNMd3BZZlBkeDY0eVExSTVoUER4RUN0S0sKTjhiR0c1TlhqdlB4a1c2L05OaHZNOVBEaUpSaVRuSnBNU3B2dldaV2hMYTliUnFxN2hOOG9tejlWSlpITmxMbgpuRkJCSFBYNEVtK2tpbDcyOG0wYkFnTUJBQUdqZ2J3d2dia3dEZ1lEVlIwUEFRSC9CQVFEQWdLRU1CMEdBMVVkCkpRUVdNQlFHQ0NzR0FRVUZCd01DQmdnckJnRUZCUWNEQVRBT0JnTlZIUTRFQndRRkFRSURCQVl3RUFZRFZSMGoKQkFrd0I0QUZBUUlEQkFVd1pnWURWUjBSQkY4d1hZSUpiRzlqWVd4b2IzTjBnaHBvWlhKdkxXRndhWE5sY25abApjaTVrWldaaGRXeDBMbk4yWTRJb2FHVnlieTFoY0dselpYSjJaWEl1WkdWbVlYVnNkQzV6ZG1NdVkyeDFjM1JsCmNpNXNiMk5oYkljRWZ3QUFBWWNFQ2dCbUxUQU5CZ2txaGtpRzl3MEJBUXNGQUFPQ0FRRUFUeFg5NisxbXlxRXkKaDg1RkdvYTMvUjI1NEtOanh0TDNMZ3FYeGl1L0JIYzlNRG53UW1QUVB1cTFGalBablB1MGhlT0VmeVQwQ3ZuTgpVQU42Q2FtZEVUMkZoWW5EdU0rT3BnNjBZTG9EeFcwNmFFbE92ejIzeUNXTXZvUm9adnZBQ0ZBRDRJZ0JqZlJXCnR4OE1UOXFXb2xQQWZXY1JXVEpFNnhvbWpxR0hkRWdWbVArT1JsYm55cHZRbEFMVmJBUlRJT29RSkQxNFlzdjYKT2xDaGRaU0hnbmV1dXhDejhBN2ZhSWpmZnBrdnQ3WU1YMG9MdGxEaGE4QW1vTi9BRDdtWUQzYzNob0JXOXRHMAoxeTMxZTVDS21XZENjSFZlWmU4MUNxSWdFeU1JYjQzZnNONGUwV05MSjRVVW05eVB2L3pucE1ETDVhNFJUQU1uCjUrUit2OFV2VGc9PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
  tls.key: 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
kind: Secret
metadata:
  name: {{ include "resource-manager.fullname" . }}
  namespace: {{ .Values.namespace}} 
type: {{ .Values.secret.type}}

---
apiVersion: v1
kind: Secret
metadata:
  name: hero-sa-secret
  namespace: {{ .Values.namespace}} 
  annotations:
    kubernetes.io/service-account.name: "{{ .Values.gataway.serviceAccountName}}"
type: kubernetes.io/service-account-token