apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "resource-manager.fullname" . }}
  namespace: {{ .Values.namespace}} 
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "resource-manager.fullname" . }}
subjects:
  - kind: ServiceAccount
    name: {{ include "resource-manager.fullname" . }}
    namespace: {{ .Values.namespace}} 

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: hero-rolebinding
  namespace: {{ .Values.namespace}} 
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: hero-role
subjects:
  - kind: ServiceAccount
    name: {{ .Values.gataway.serviceAccount.name}}
    namespace: {{ .Values.namespace}}