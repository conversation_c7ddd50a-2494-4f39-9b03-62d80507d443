apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "resource-manager.fullname" . }}
  namespace: {{ .Values.namespace}} 
spec:
  selector:
    matchLabels:
      {{- include "resource-manager.selectorLabels" . | nindent 6 }}
  replicas: {{ .Values.replicaCount }}
  template:
    metadata:
      labels:
        {{- include "resource-manager.selectorLabels" . | nindent 8 }}
    spec:
      serviceAccountName: {{ include "resource-manager.fullname" . }}
      containers:
      - name: {{ .Chart.Name }}
        image: {{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        volumeMounts:
        - name: hero-apiserver-configmap
          mountPath: {{ .Values.image.containers.configmapmount }}
        - name: hero-apiserver-secret
          mountPath: {{ .Values.image.containers.secretmount }} 
      volumes:
      - name: hero-apiserver-configmap
        configMap:
          defaultMode: 420
          name: {{ include "resource-manager.fullname" . }}     
      - name: hero-apiserver-secret
        secret:
          secretName: {{ include "resource-manager.fullname" . }}