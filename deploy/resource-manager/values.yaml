# Default values for resource-manager.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

namespace: heros-controllers-system

rules:
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["get","list"]
  - apiGroups: [""]
    resources: ["nodes"]
    verbs: ["get","list"]
  - apiGroups: ["monitoring.hero.ai"]
    resources: ["*"]
    verbs: ["get","list"]

secret:
  type: kubernetes.io/tls

image:
  repository: registry.bitahub.com:5000/leinaoyun-tag/hero-apiserver
  pullPolicy: Always
  tag: "v0.0.12"
  containers:
    secretmount: /etc/hero-apiserver-cert
    configmapmount: /etc/hero-apiserver


apiservice:
  name: v1.monitoring.hero.ai
  group: monitoring.hero.ai
  groupPriorityMinimum: 1000
  versionPriority: 15
  version: v1


service:
  type: ClusterIP
  port: 8080

configmap:
  hero_apiserver.yaml: |-
    logLevel: 5
    http:
      addr: 0.0.0.0
      port: 8080
      tls: true
    tls:
      cert: /etc/hero-apiserver-cert/tls.crt
      key: /etc/hero-apiserver-cert/tls.key
    loki:
      addr: ***********
      port: 51977
    prometheus:
      addr: ***********
      port: 9233
    rocketmq:
      endpoints:
        - http://***********:9876
      taskGroup: file-proxy
      topic: file-proxy
      accessKey: ""
      secretKey: ""
    minio:
      accessKey: LEINAOYUNOS
      secretKey: wJalrXUtnFEMI/K7MDENG/bPxRfiCYLEINAOYUNKEY
      endpoint: hero-dev-miniogw.cnbita.com
      useSSL: true

  volcano-scheduler.yaml: |-
    actions: "enqueue, allocate, backfill"
    tiers:
      - plugins:
        - name: priority
        - name: gang
        - name: conformance
      - plugins:
        - name: drf
        - name: predicates
        - name: nodeorder
        - name: proportion
          enableJobEnqueued: false
        - name: binpack
          arguments:
            binpack.weight: 10
            binpack.resources: nvidia.com/gpu,nvidia.com/nvidia-rtx-3090,nvidia.com/a100-sxm4-80gb,nvidia.com/a100-80gb-pcie,nvidia.com/nvidia-xp,huawei.com/Ascend910
            binpack.resources.nvidia.com/gpu: 10
            binpack.resources.nvidia.com/nvidia-rtx-3090: 10
            binpack.resources.nvidia.com/a100-sxm4-80gb: 10
            binpack.resources.nvidia.com/a100-80gb-pcie: 10
            binpack.resources.nvidia.com/nvidia-xp: 10
            binpack.resources.huawei.com/Ascend910: 10

gataway:
  serviceAccount:
    name: hero-sa
  rules:
  - apiGroups: ["monitoring.hero.ai","monitoring.coreos.com","configmaps"]
    resources: ["*"]
    verbs: ["*"]
