package model

import (
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type ResourcePoolSpec struct {
	// Nodes        []string                   `json:"nodes,omitempty"`
	// Capabilities map[string]v1.ResourceList `json:"capabilities,omitempty"`
	// Demo         map[string]v1.ResourceList `json:"demo,omitempty"`
	Description string `json:"Description,omitempty"`
}

type ResourcePoolState string

const (
	ResourcePoolStateOpen    ResourcePoolState = "Open"    //运行中
	ResourcePoolStateClosed  ResourcePoolState = "Closed"  //已关闭
	ResourcePoolStateFailed  ResourcePoolState = "Failed"  //失败
	ResourcePoolStateClosing ResourcePoolState = "Closing" //关闭中
)

type ResourcePoolStatus struct {
	Nodes        []string                   `json:"nodes,omitempty"`
	Capabilities map[string]v1.ResourceList `json:"capabilities,omitempty"`

	State ResourcePoolState `json:"state,omitempty"`
	// +optional
	CreateTime string `json:"createTime,omitempty"`
	// +optional
	Allocated map[string]v1.ResourceList `json:"allocated"`
	// +optional
	Idle v1.ResourceList `json:"idle"`
}
type ResourcePool struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   ResourcePoolSpec   `json:"spec,omitempty"`
	Status ResourcePoolStatus `json:"status,omitempty"`
}

type ResourceOccupied struct {
	CPUOccupied float64            `json:"cpu,omitempty"`
	MemOccupied float64            `json:"memory,omitempty"`
	GpuOccupied map[string]float64 `json:"gpu,omitempty"`
}

type PodResourceOccupied struct {
	PodName              string             `json:"pod_name,omitempty"`
	ResourceOccupiedList []ResourceOccupied `json:"pod_occupied,omitempty"`
}
type NodeResourceOccupied struct {
	NodeName                string                `json:"node_name,omitempty"`
	PodResourceOccupiedList []PodResourceOccupied `json:"node_occupied,omitempty"`
}

type ResourcePoolResourceOccupied struct {
	ResourcePoolName     string                 `json:"resource_pool_name,omitempty"`
	NodeResourceOccupied []NodeResourceOccupied `json:"resource_pool_occupied,omitempty"`
}
