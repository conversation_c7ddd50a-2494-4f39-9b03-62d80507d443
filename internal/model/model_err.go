package model

import (
	"fmt"
	"net/http"

	"github.com/emicklei/go-restful"
)

var message map[uint32]string

func init() {
	message = map[uint32]string{
		OK:                   "SUCCESS",
		QueryMetricNotFound:  "Query metrics do not exist",
		ParseReqFailed:       "Parse request param failed",
		QueryFailed:          "Query failed, An error occurred in some or all of the query statements",
		ErrParamConflict:     "'time' and the combination of 'start' and 'end' are mutually exclusive",
		ErrInvalidStartEnd:   "'start' must be before 'end'",
		ErrParameterNotfound: "Parmameter [%s] not found",
	}
}

func ErrMsg(errcode uint32) string {
	if msg, ok := message[errcode]; ok {
		return msg
	}
	return "服务内部错误"
}

type Error struct {
	Message string `json:"message" description:"error message"`
}

var None = Error{Message: "success"}

func (e Error) Error() string {
	return e.Message
}

func Wrap(err error) error {
	return Error{Message: err.Error()}
}

func NewEerrors(format string, args ...interface{}) error {
	return Error{Message: fmt.Sprintf(format, args...)}
}

func GetServiceErrorCode(err error) int {
	if svcErr, ok := err.(restful.ServiceError); ok {
		return svcErr.Code
	}
	return http.StatusInternalServerError
}
