package model

import (
	"time"
)

const (
	DefaultPatterns1 = ``
	DefaultPatterns2 = `| json |line_format "{{.log}}"`
)

// Log search result
type Logs struct {
	Total   int64         `json:"total" description:"total number of matched results"`
	Records []interface{} `json:"records,omitempty" description:"actual array of results"`
}

type Record struct {
	Log       string `json:"log,omitempty" description:"log message"`
	Timestamp int64  `json:"timestamp,omitempty" description:"log timestamp"`
	Namespace string `json:"namespace,omitempty" description:"namespace"`
	Pod       string `json:"pod,omitempty" description:"pod name"`
	Container string `json:"container,omitempty" description:"container name"`
}

// Log statistics result
type Statistics struct {
	Containers int64 `json:"containers" description:"total number of containers"`
	Logs       int64 `json:"logs" description:"total number of logs"`
}

// Log count result by interval
type Histogram struct {
	Total   int64    `json:"total" description:"total number of logs"`
	Buckets []Bucket `json:"histograms" description:"actual array of histogram results"`
}

type Bucket struct {
	Time  int64 `json:"time" description:"timestamp"`
	Count int64 `json:"count" description:"total number of logs at intervals"`
}

// General query conditions
type SearchFilter struct {
	NamespaceFilter []string
	NamespaceSearch []string
	PodSearch       []string
	PodFilter       []string
	ContainerFilter []string
	Keywords        string

	Starttime time.Time
	Endtime   time.Time

	Interval string
	Sort     string
	Showtime string
	From     int64
	Size     int64
}
