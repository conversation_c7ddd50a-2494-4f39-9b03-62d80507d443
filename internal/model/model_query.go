package model

import "time"

type ListResult struct {
	Items      []interface{} `json:"items,omitempty"`
	TotalItems int           `json:"totalItems"`
}

type Level string

const (
	ClusterLevel Level = "cluster"
	NodeLevel    Level = "node"
	JobLevel     Level = "job"
)

type LogQueryReq struct {
	Operation       string
	NamespaceFilter string
	NamespaceSearch string
	WorkloadFilter  string
	WorkloadSearch  string
	PodFilter       string
	PodSearch       string
	KeywordFilter   string
	ContainerFilter string
	ContainerSearch string
	LogSearch       string
	StartTime       time.Time
	EndTime         time.Time
	Interval        string
	Sort            string
	ShowTime        string
	From            int64
	Size            int64
	BucketName      string
	BucketFilePath  string
	StorageType     string
	TaskID          string
}

type EventQueryReq struct {
	TaskID          string
	StartTime       time.Time
	EndTime         time.Time
	NamespaceFilter string
}

type QueryOptions struct {
	AloneFromRangeTime bool
	MetricsName        string        // 指标名称
	JobName            string        // pod 名称
	ResourcePoolName   string        // 资源池名称
	Namespace          string        // 命名空间
	NodeName           string        // 节点名称
	TaskType           string        // 任务类型
	ReponseCode        string        // 请求响应码
	Start              time.Time     // 查询的开始时间
	End                time.Time     // 查询的结束时间
	Time               time.Time     // 查询的当前时间
	Step               time.Duration // 查询的时间间隔
	ModeName           string        // 计算卡类型
	UUID               string        // 计算卡UUID
	StatisticalTime    string        // 数据统计范围（10m 1h）
}
