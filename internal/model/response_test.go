package model

import (
	"encoding/json"
	"fmt"
	"testing"
)

// 测试数据反序列化
func TestParseResponse(t *testing.T) {
	responseStr := `{
		"status": "success",
		"data": {
			"resultType": "streams",
			"result": [
				{
					"stream": {
						"namespace": "default",
						"pod": "test-pod",
						"container": "test-container"
					},
					"values": [
						["1625247600", "Log entry 1"],
						["1625247660", "Log entry 2"]
					]
				}
			]
		}
	}`

	var response Response
	err := json.Unmarshal([]byte(responseStr), &response)
	if err != nil {
		fmt.Printf("Error unmarshalling response: %v", err)
		return
	}

	// 调用 ParseResponse 解析响应数据
	structResp, err := ParseResponse(&response)
	if err != nil {
		fmt.Printf("Error parsing response: %v", err)
		return
	}

	// 打印结果
	for _, stream := range structResp.Streams {
		for _, entry := range stream.Values {
			fmt.Printf("Timestamp: %s, Log: %s\n", entry.Timestamp, entry.Line)
		}
	}
}
