package monitor

import (
	"context"
	"fmt"

	"net/http"
	"net/http/httptest"
	"testing"

	query "hero-apiserver/internal/model"

	"github.com/prometheus/client_golang/api"
	prometheusv1 "github.com/prometheus/client_golang/api/prometheus/v1"
	"gopkg.in/yaml.v2"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/fake"
)

var cardHandler = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
	if r.URL.Path == "/api/v1/query" {
		w.<PERSON>er().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		fmt.Fprintf(w, `{
			"status": "success",
			"data": {
				"resultType": "vector",
				"result": [
					{
						"metric": {"UUID": "GPU-4179bc9c-18fa-8984-49cb-715fd8361a0f", "gpu": "0","modelName": "NVIDIA GeForce RTX 3090"},
						"value": [1625247600, "1"]
					}
				]
			}
		}`)
	} else if r.URL.Path == "/api/v1/query_range" {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		fmt.Fprintf(w, `{
			"status": "success",
			"data": {
				"resultType": "matrix",
				"result": [
					{
						"metric": {"UUID": "GPU-4179bc9c-18fa-8984-49cb-715fd8361a0f", "gpu": "0","modelName": "NVIDIA GeForce RTX 3090"},
						"values": [
							[1625247600, "1"],
							[1625247660, "0"],
							[1625247720, "1"]
						]
					}
				]
			}
		}`)
	} else {
		http.NotFound(w, r)
	}
})

var configMap = &v1.ConfigMap{
	ObjectMeta: metav1.ObjectMeta{
		Name:      "isolation-config", // ConfigMap 的名称
		Namespace: "kube-system",      // ConfigMap 的命名空间
	},
	Data: map[string]string{
		"config.yaml": `nodeName: yigou-dev-102-69
info:
- uuid: GPU-4179bc9c-18fa-8984-49cb-715fd8361a0f
  action: recover`,
	},
}

func TestGPUCardList(t *testing.T) {

	clientset := fake.NewSimpleClientset()
	_, err := clientset.CoreV1().ConfigMaps("kube-system").Create(context.TODO(), configMap, metav1.CreateOptions{})
	if err != nil {
		fmt.Printf("Error creating ConfigMap: %v\n", err)
		return
	}
	server := httptest.NewServer(cardHandler)
	defer server.Close()
	client, err := api.NewClient(api.Config{
		Address: server.URL,
	})
	if err != nil {
		t.Fatalf("Error creating client: %v", err)
	}
	ps := NewPrometheusServer(prometheusv1.NewAPI(client), nil, clientset)
	q := query.QueryOptions{
		NodeName: "yigou-dev-102-69",
	}
	ic, _ := ps.GPUCardList(q)
	if len(ic.Cards) != 1 {
		t.Fatalf("Error Query GPU Card List : %v", err)
	}

}

func TestGPUCardLock(t *testing.T) {
	clientset := fake.NewSimpleClientset()
	_, err := clientset.CoreV1().ConfigMaps("kube-system").Create(context.TODO(), configMap, metav1.CreateOptions{})
	if err != nil {
		fmt.Printf("Error creating ConfigMap: %v\n", err)
		return
	}
	server := httptest.NewServer(cardHandler)
	defer server.Close()
	client, err := api.NewClient(api.Config{
		Address: server.URL,
	})
	if err != nil {
		t.Fatalf("Error creating client: %v", err)
	}
	ps := NewPrometheusServer(prometheusv1.NewAPI(client), nil, clientset)
	q := query.QueryOptions{
		NodeName: "yigou-dev-102-69",
		UUID:     "GPU-4179bc9c-18fa-8984-49cb-715fd8361a0f",
	}
	err = ps.GPUCardLock(q)
	if err != nil {
		t.Fatalf("Lock Failed: %v", err)
	}
	cm, err := ps.clientSet.CoreV1().ConfigMaps("kube-system").Get(context.TODO(), IsolationConfig, metav1.GetOptions{})
	if err != nil {
		t.Fatalf("Lock Failed: %v", err)
	}
	var isolationCards IsolationCards
	err = yaml.Unmarshal([]byte(cm.Data["config.yaml"]), &isolationCards)
	if err != nil {
		t.Fatalf("Lock Failed: %v", err)
	}
	if isolationCards.Cards[0].Action != "lock" {
		t.Fatalf("Lock Failed: %v", err)
	}
}

func TestGPUCardRecover(t *testing.T) {
	clientset := fake.NewSimpleClientset()
	_, err := clientset.CoreV1().ConfigMaps("kube-system").Create(context.TODO(), configMap, metav1.CreateOptions{})
	if err != nil {
		fmt.Printf("Error creating ConfigMap: %v\n", err)
		return
	}
	server := httptest.NewServer(cardHandler)
	defer server.Close()
	client, err := api.NewClient(api.Config{
		Address: server.URL,
	})
	if err != nil {
		t.Fatalf("Error creating client: %v", err)
	}
	ps := NewPrometheusServer(prometheusv1.NewAPI(client), nil, clientset)
	q := query.QueryOptions{
		NodeName: "yigou-dev-102-69",
		UUID:     "GPU-4179bc9c-18fa-8984-49cb-715fd8361a0f",
	}
	err = ps.GPUCardRecover(q)
	if err != nil {
		t.Fatalf("Lock Failed: %v", err)
	}
	cm, err := ps.clientSet.CoreV1().ConfigMaps("kube-system").Get(context.TODO(), IsolationConfig, metav1.GetOptions{})
	if err != nil {
		t.Fatalf("Lock Failed: %v", err)
	}
	var isolationCards IsolationCards
	err = yaml.Unmarshal([]byte(cm.Data["config.yaml"]), &isolationCards)
	if err != nil {
		t.Fatalf("Lock Failed: %v", err)
	}
	if isolationCards.Cards[0].Action != "recover" {
		t.Fatalf("Lock Failed: %v", err)
	}
}
