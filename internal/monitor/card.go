package monitor

import (
	"context"
	"fmt"
	query "hero-apiserver/internal/model"
	"time"

	"gopkg.in/yaml.v2"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type IsolationCard struct {
	UUID     string `yaml:"uuid,omitempty"`
	Action   string `yaml:"action,omitempty"`
	ID       string `yaml:"id,omitempty"`
	CardType string `yaml:"cardtype,omitempty"`
	Status   int    `yaml:"status,omitempty"`
}

type IsolationCards struct {
	NodeName string           `yaml:"nodeName,omitempty"`
	Cards    []*IsolationCard `yaml:"info,omitempty"`
}

var (
	RecoverAction   string = "recover"
	LockAction      string = "lock"
	Namespace       string = "kube-system"
	IsolationConfig string = "isolation-config"
	ConfigMapKey    string = "config.yaml"
)

func (p PrometheusServer) getNodeCards(nodeName string, cardlist *IsolationCards) error {
	var q query.QueryOptions
	q.MetricsName = "node_gpu_card_info"
	q.Time = time.Now()
	q.NodeName = nodeName
	reslut := p.NodeQueryCurrentTime(q)

	for _, metric := range reslut {
		for _, md := range metric.MetricData.Data {
			if uuid, ok1 := md.Meta["UUID"]; ok1 {
				cardlist.Cards = append(cardlist.Cards,
					&IsolationCard{
						UUID:     uuid,
						Action:   RecoverAction,
						ID:       md.Meta["gpu"],
						CardType: md.Meta["modelName"],
						Status:   1, // 1 表示卡状态正常，0表示卡状态异常
					})
			}

		}
	}
	return nil

}

func (p PrometheusServer) SyncGPUCardList() error {
	cardlist := &IsolationCards{Cards: make([]*IsolationCard, 0)}
	err := p.getNodeCards("", cardlist)
	if err != nil || len(cardlist.Cards) == 0 {
		return fmt.Errorf("get node gpu card list failed from prometheus")
	}

	configMap, err := p.clientSet.CoreV1().ConfigMaps(Namespace).Get(context.TODO(), IsolationConfig, metav1.GetOptions{})
	if err != nil {
		return err
	}

	reslut := &IsolationCards{Cards: make([]*IsolationCard, 0)}
	for _, card := range cardlist.Cards {
		reslut.Cards = append(reslut.Cards, &IsolationCard{UUID: card.UUID, Action: card.Action})
	}
	yamlData, err := yaml.Marshal(&reslut)
	if err != nil {
		return err
	}
	configMap.Data[ConfigMapKey] = string(yamlData)

	_, err = p.clientSet.CoreV1().ConfigMaps(Namespace).Update(context.TODO(), configMap, metav1.UpdateOptions{})
	if err != nil {
		return err
	}

	return nil
}

func (p PrometheusServer) GPUCardList(q query.QueryOptions) (*IsolationCards, error) {

	cardlist := &IsolationCards{NodeName: q.NodeName, Cards: make([]*IsolationCard, 0)}
	err := p.getNodeCards(q.NodeName, cardlist)
	if err != nil || len(cardlist.Cards) == 0 {
		return nil, err
	}

	configMap, err := p.clientSet.CoreV1().ConfigMaps(Namespace).Get(context.TODO(), IsolationConfig, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}

	var isolationCards IsolationCards
	err = yaml.Unmarshal([]byte(configMap.Data["config.yaml"]), &isolationCards)
	if err != nil {
		return nil, err
	}

	if len(isolationCards.Cards) == 0 {
		err := p.SyncGPUCardList()
		if err != nil {
			return nil, err
		}
	}

	for _, icard := range isolationCards.Cards {
		for _, ncard := range cardlist.Cards {
			if icard.UUID == ncard.UUID {
				ncard.Action = icard.Action
			}
		}
	}

	return cardlist, nil

}

func (p PrometheusServer) GPUCardLock(q query.QueryOptions) error {
	err := p.GPUCardAction(q, LockAction)
	if err != nil {
		return err
	}
	return nil
}

func (p PrometheusServer) GPUCardRecover(q query.QueryOptions) error {
	err := p.GPUCardAction(q, RecoverAction)
	if err != nil {
		return err
	}
	return nil
}

func (p PrometheusServer) GPUCardAction(q query.QueryOptions, action string) error {
	configMap, err := p.clientSet.CoreV1().ConfigMaps(Namespace).Get(context.TODO(), IsolationConfig, metav1.GetOptions{})
	if err != nil {
		return err
	}

	var isolationCards IsolationCards
	err = yaml.Unmarshal([]byte(configMap.Data["config.yaml"]), &isolationCards)
	if err != nil {
		return err
	}

	for _, card := range isolationCards.Cards {
		if card.UUID == q.UUID {
			card.Action = action
		}
	}
	yamlData, err := yaml.Marshal(&isolationCards)
	if err != nil {
		return err
	}

	configMap.Data[ConfigMapKey] = string(yamlData)

	_, err = p.clientSet.CoreV1().ConfigMaps(Namespace).Update(context.TODO(), configMap, metav1.UpdateOptions{})
	if err != nil {
		return err
	}

	return nil
}
