package monitor

import (
	"context"
	"fmt"
	query "hero-apiserver/internal/model"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"testing"
	"time"

	v1 "k8s.io/api/core/v1"

	"github.com/prometheus/client_golang/api"
	prometheusv1 "github.com/prometheus/client_golang/api/prometheus/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/fake"
)

var handler = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
	if r.URL.Path == "/api/v1/query" {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		fmt.Fprintf(w, `{
			"status": "success",
			"data": {
				"resultType": "vector",
				"result": [
					{
						"metric": {"__name__": "up"},
						"value": [1625247600, "1"]
					}
				]
			}
		}`)
	} else if r.URL.Path == "/api/v1/query_range" {
		w.<PERSON><PERSON>().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		fmt.Fprintf(w, `{
			"status": "success",
			"data": {
				"resultType": "matrix",
				"result": [
					{
						"metric": {"__name__": "up"},
						"values": [
							[1625247600, "1"],
							[1625247660, "0"],
							[1625247720, "1"]
						]
					}
				]
			}
		}`)
	} else {
		http.NotFound(w, r)
	}
})

func TestFillZero(t *testing.T) {
	start := time.Now()
	timeRange := prometheusv1.Range{
		Start: start,
		End:   start.Add(time.Hour),
		Step:  time.Minute,
	}
	res := &MetricData{ServerName: map[string]string{"jobNames": "1"}, Data: make([]*MetaData, 0)}

	fillZeroRangeTime(res, timeRange)

	if len(res.Data[0].Data) != 61 {
		t.Fatal("file zero failed")
	}

}

func TestFillZeroRangeTime(t *testing.T) {
	start, _ := parseTime("1625247600")
	end, _ := parseTime("1625247630")
	res := &MetricData{ServerName: map[string]string{"jobNames": "1"}, Data: make([]*MetaData, 0)}
	timeRange := prometheusv1.Range{
		Start: start,
		End:   end,
		Step:  15 * time.Second,
	}
	ms := fillZeroRangeTime(res, timeRange)
	if len(ms.Data[0].Data) != 3 {
		t.Fatal("query job metrics failed", ms.Data[0].Data)
	}
}

func TestFillZeroCurrentTime(t *testing.T) {
	time, _ := parseTime("1718355232")
	res := &MetricData{ServerName: map[string]string{"jobNames": "1"}, Data: make([]*MetaData, 0)}
	ms := fillZeroCurrentTime(res, time)
	if len(ms.Data[0].Data) != 1 {
		t.Fatal("query job metrics failed")
	}
}

// func TestMergeMetric(t *testing.T) {
// 	var res []*Metric
// 	res = append(res, &Metric{
// 		MetricIndex: 0,
// 		MetricData: &MetricData{
// 			ServerName: map[string]string{"jobNames": "1"},
// 			Data: []*MetaData{
// 				{
// 					Meta: map[string]string{"test": "true"},
// 					Data: []Point{
// 						{Time: time.Now().Unix(), Value: 0},
// 					},
// 				},
// 			},
// 		}},
// 	)
// 	res = append(res, &Metric{
// 		MetricIndex: 1,
// 		MetricData: &MetricData{
// 			ServerName: map[string]string{"jobNames": "1"},
// 			Data: []*MetaData{
// 				{
// 					Meta: map[string]string{"test": "true"},
// 					Data: []Point{
// 						{Time: time.Now().Add(time.Minute).Unix(), Value: 1},
// 					},
// 				},
// 			},
// 		}},
// 	)
// 	res = MergeMetric(res)
// 	if len(res) != 1 {
// 		t.Fatal("query job metrics failed")
// 	}
// }

func TestParseTime(t *testing.T) {
	timeStr := "1718358832"
	timeInt, err := strconv.ParseInt(timeStr, 10, 64)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(timeInt)
}

func parseTime(timeStr string) (time.Time, error) {
	timeInt, err := strconv.ParseInt(timeStr, 10, 64)
	return time.Unix(timeInt, 0), err
}

func TestNodeMetricsByCurrentTime(t *testing.T) {
	server := httptest.NewServer(handler)
	defer server.Close()
	client, err := api.NewClient(api.Config{
		Address: server.URL,
	})
	if err != nil {
		t.Fatalf("Error creating client: %v", err)
	}
	p := NewPrometheusServer(prometheusv1.NewAPI(client), nil, nil)

	time, _ := parseTime("1625247600")
	q := query.QueryOptions{
		MetricsName: "cluster_total_cpu_core",
		Time:        time,
	}
	ms := p.NodeQueryCurrentTime(q)
	if len(ms[0].MetricData.Data[0].Data) != 1 {
		t.Fatal("query job metrics failed")
	}

}

func TestNodeMetricsByRangeTime(t *testing.T) {
	server := httptest.NewServer(handler)
	defer server.Close()
	client, err := api.NewClient(api.Config{
		Address: server.URL,
	})
	if err != nil {
		t.Fatalf("Error creating client: %v", err)
	}
	p := NewPrometheusServer(prometheusv1.NewAPI(client), nil, nil)

	start, _ := parseTime("1625247600")
	end, _ := parseTime("1625247630")
	q := query.QueryOptions{
		MetricsName:        "node_total_gpu_num",
		NodeName:           "yigou-stg-101-68",
		Start:              start,
		End:                end,
		Step:               time.Minute,
		AloneFromRangeTime: false,
	}
	ms := p.NodeQueryRangeTime(q)
	if len(ms[0].MetricData.Data[0].Data) != 2 {
		t.Fatal("query job metrics failed")
	}

}

func TestClusterMetricsByRangeTime(t *testing.T) {
	server := httptest.NewServer(handler)
	defer server.Close()
	client, err := api.NewClient(api.Config{
		Address: server.URL,
	})
	if err != nil {
		t.Fatalf("Error creating client: %v", err)
	}
	p := NewPrometheusServer(prometheusv1.NewAPI(client), nil, nil)

	start, _ := parseTime("1625247600")
	end, _ := parseTime("1625247630")
	q := query.QueryOptions{
		MetricsName:        "cluster_total_cpu_core",
		Start:              start,
		End:                end,
		Step:               time.Minute,
		AloneFromRangeTime: false,
	}
	ms := p.ClusterQueryRangeTime(q)
	if len(ms[0].MetricData.Data[0].Data) != 2 {
		t.Fatal("query job metrics failed")
	}
}

func TestClusterMetricsByCurrentTime(t *testing.T) {
	server := httptest.NewServer(handler)
	defer server.Close()
	client, err := api.NewClient(api.Config{
		Address: server.URL,
	})
	if err != nil {
		t.Fatalf("Error creating client: %v", err)
	}
	p := NewPrometheusServer(prometheusv1.NewAPI(client), nil, nil)

	time, _ := parseTime("1625247600")
	q := query.QueryOptions{
		MetricsName: "cluster_total_cpu_core",
		Time:        time,
	}
	ms := p.ClusterQueryCurrentTime(q)
	if len(ms[0].MetricData.Data[0].Data) != 1 {
		t.Fatal("query job metrics failed")
	}
}

func TestJobMetricsByRangeTime(t *testing.T) {
	server := httptest.NewServer(handler)
	defer server.Close()
	client, err := api.NewClient(api.Config{
		Address: server.URL,
	})
	if err != nil {
		t.Fatalf("Error creating client: %v", err)
	}
	p := NewPrometheusServer(prometheusv1.NewAPI(client), nil, nil)

	start, _ := parseTime("1625247600")
	end, _ := parseTime("1625247630")
	q := query.QueryOptions{
		MetricsName:        "job_mem_utilization",
		JobName:            "model-repository-image-to-text-01-v1-7f4889d4bd-6zg5x",
		Start:              start,
		End:                end,
		Step:               time.Minute,
		AloneFromRangeTime: false,
	}
	ms := p.JobQueryRangeTime(q)
	if len(ms[0].MetricData.Data[0].Data) != 2 {
		t.Fatal("query job metrics failed")
	}
}

func TestJobMetricsByCurrentTime(t *testing.T) {
	server := httptest.NewServer(handler)
	defer server.Close()
	client, err := api.NewClient(api.Config{
		Address: server.URL,
	})
	if err != nil {
		t.Fatalf("Error creating client: %v", err)
	}
	p := NewPrometheusServer(prometheusv1.NewAPI(client), nil, nil)

	start, _ := parseTime("1625247600")
	end, _ := parseTime("1625247630")
	q := query.QueryOptions{
		MetricsName:        "job_mem_utilization",
		JobName:            "model-repository-image-to-text-01-v1-7f4889d4bd-6zg5x",
		Start:              start,
		End:                end,
		Step:               time.Minute,
		AloneFromRangeTime: true,
	}
	ms := p.JobQueryRangeTime(q)
	res := AloneValueByRangeTime(ms)
	if len(res[0].MetricData.Data[0].Data) != 1 {
		t.Fatal("query job metrics failed")
	}

}

func TestClusterNodeNameInfo(t *testing.T) {
	clientset := fake.NewSimpleClientset()

	node := &v1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "example-node",
			Labels: map[string]string{
				"kubernetes.io/hostname": "example-hostname",
			},
		},
		Status: v1.NodeStatus{
			Conditions: []v1.NodeCondition{
				{
					Type:   v1.NodeReady,
					Status: v1.ConditionTrue,
				},
			},
		},
	}
	_, err := clientset.CoreV1().Nodes().Create(context.TODO(), node, metav1.CreateOptions{})
	if err != nil {
		t.Fatal(err.Error())
	}

	ps := NewPrometheusServer(nil, nil, clientset)

	res, err := ps.ClusterNodeNameInfo()
	if err != nil {
		t.Fatal(err.Error())
	}
	if res.ComponentDim != "node" || res.Component[0].ComponentName != "example-node" && res.Component[0].HealthStatus != 1 {
		t.Fatal("error")
	}

}

func TestClusterPodInfo(t *testing.T) {
	clientset := fake.NewSimpleClientset()
	pod1 := &v1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "pod-1",
			Namespace: "example",
			Labels: map[string]string{
				"app": "example-app",
			},
		},
		Spec: v1.PodSpec{
			Containers: []v1.Container{
				{
					Name:  "container-1",
					Image: "nginx:latest",
				},
			},
		},
		Status: v1.PodStatus{
			ContainerStatuses: []v1.ContainerStatus{
				{
					Ready: true,
				},
			},
		},
	}

	pod2 := &v1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "pod-2",
			Namespace: "example",
			Labels: map[string]string{
				"app": "example-app",
			},
		},
		Spec: v1.PodSpec{
			Containers: []v1.Container{
				{
					Name:  "container-2",
					Image: "nginx:latest",
				},
			},
		},
		Status: v1.PodStatus{
			ContainerStatuses: []v1.ContainerStatus{
				{
					Ready: false,
				},
			},
		},
	}
	_, err := clientset.CoreV1().Pods("example").Create(context.TODO(), pod1, metav1.CreateOptions{})
	if err != nil {
		t.Fatal(err)
	}

	_, err = clientset.CoreV1().Pods("example").Create(context.TODO(), pod2, metav1.CreateOptions{})
	if err != nil {
		t.Fatal(err)
	}

	ps := NewPrometheusServer(nil, nil, clientset)

	res, err := ps.ClusterPodInfo()

	if err != nil {
		t.Fatal(err.Error())
	}
	if len(res.Components) != 2 {
		t.Fatal("err")
	}

	for _, components := range res.Components {
		for _, pods := range components.Component {
			if pods.ComponentName == "pod-1" && pods.HealthStatus != 1 {
				t.Fatal("pod status sync failed")
			}
			if pods.ComponentName == "pod-2" && pods.HealthStatus != 0 {
				t.Fatal("pod status sync failed")
			}
		}
	}

}

func TestENVExsited(t *testing.T) {
	if os.Getenv("NACOS_SERVER_URL") == "" || os.Getenv("JASYPT_ENCRYPTOR_PASSWORD") == "" || os.Getenv("INIT_MODE") == "" {
		t.Fatal("env nacos is nil")
	}
}
