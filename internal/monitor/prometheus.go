package monitor

import (
	"context"
	"fmt"
	"hero-apiserver/internal/config"
	query "hero-apiserver/internal/model"
	"math"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/prometheus/client_golang/api"
	apiv1 "github.com/prometheus/client_golang/api/prometheus/v1"
	"github.com/prometheus/common/model"
	log "github.com/sirupsen/logrus"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
)

type Metric struct {
	ErrorMes    string      `json:"query_err,omitempty" description:"error of query result"`
	MetricName  string      `json:"metric_name,omitempty" description:"name of metric "`
	MetricIndex int         `json:"metric_index,omitempty" description:"index of metric "`
	MetricData  *MetricData `json:"metric_data,omitempty" description:"actual metric result"`
}

type MetricData struct {
	ServerName map[string]string `json:"query_info,omitempty" description:"query name"`
	Data       []*MetaData       `json:"query_data,omitempty" description:"all of the metric query result"`
}

type MetaData struct {
	Meta map[string]string `json:"meta" description:"metadata of result"`
	Data []Point           `json:"data" description:"value metric result"`
}

type Point struct {
	Time  int64   `json:"time,"`
	Value float64 `json:"value"`
}

type Options struct {
	Endpoint string `json:"endpoint,omitempty" yaml:"endpoint"`
}

func NewPrometheusOptions(endpoint string) *Options {
	return &Options{
		Endpoint: endpoint,
	}
}

type PrometheusServer struct {
	client        apiv1.API
	dynamicClient dynamic.Interface
	clientSet     kubernetes.Interface
}

type ByTime []Point

func (a ByTime) Len() int           { return len(a) }
func (a ByTime) Less(i, j int) bool { return a[i].Time < a[j].Time }
func (a ByTime) Swap(i, j int)      { a[i], a[j] = a[j], a[i] }

const (
	queryCount              = 10000
	DefaultDurationSecond   = 15 * time.Second
	DefaultDurationMinute   = 15 * time.Minute
	CUSTOMCOMPUTERATE       = "leinaoyun.com/customComputeRate"
	ALONEVALUEBYRANGETIME   = "leinaoyun.com/aloneValueByRangeTime"
	FILEZERO                = "leinaoyun.com/fillZeroDurition"
	AllZERO                 = "leinaoyun.com/allZeroDurition"
	QUERYDURITION           = "leinaoyun.com/queryTimeDurition"
	RESOURCEPOOLLABELPREFIX = "resourcepool.system.hero.ai"
	DEFAULTRESOURCEPOOLNAME = "default"
	TRAINNINGNODELABEL      = "node-role.kubernetes.io/training=true"
	MERGEMETRICSBATCHES     = "leinaoyun.com/mergeMetricsBatches"
	COLLECTIONPOINTNUMS     = "leinaoyun.com/collectionPointNums"
	TOTALCOUNT              = "leinaoyun.com/totalPointNums"
	USERNAMESPACE           = "hero-user"
)

func NewPrometheusServer(prometheusClient apiv1.API, dynamicClient dynamic.Interface, clientSet kubernetes.Interface) PrometheusServer {
	return PrometheusServer{client: prometheusClient, dynamicClient: dynamicClient, clientSet: clientSet}
}
func NewPrometheusClient(options *Options) (apiv1.API, error) {
	cfg := api.Config{
		Address: options.Endpoint,
	}
	client, err := api.NewClient(cfg)
	if err != nil {
		return nil, err
	}
	return apiv1.NewAPI(client), nil
}

func GetTimeRangeList(start, end time.Time, step time.Duration) ([]time.Time, []time.Time) {
	var ti time.Time
	count := 1
	epochStartTimeList := make([]time.Time, 0)
	epochEndTimeList := make([]time.Time, 0)
	epochStartTimeList = append(epochStartTimeList, start)
	for ti = start; ti.Before(end); ti = ti.Add(step) {
		if count%queryCount == 0 {
			epochEndTimeList = append(epochEndTimeList, ti)
			epochStartTimeList = append(epochStartTimeList, ti)
		}
		count++
	}
	epochEndTimeList = append(epochEndTimeList, ti)
	return epochStartTimeList, epochEndTimeList

}

func ParseQueryRangeResp(value model.Value, KeyNames map[string]string) *MetricData {

	res := &MetricData{ServerName: KeyNames, Data: make([]*MetaData, 0)}
	data, _ := value.(model.Matrix)
	for _, v := range data {
		t := &MetaData{Meta: map[string]string{
			COLLECTIONPOINTNUMS: strconv.Itoa(len(v.Values)),
		}, Data: make([]Point, 0)}
		for k, v := range v.Metric {
			t.Meta[string(k)] = string(v)
		}
		for _, k := range v.Values {
			t.Data = append(t.Data, Point{Time: k.Timestamp.Unix(), Value: decimal(float64(k.Value))})
		}
		res.Data = append(res.Data, t)
	}
	return res
}

func ParseQueryCurrentResp(value model.Value, KeyNames map[string]string) *MetricData {
	res := &MetricData{ServerName: KeyNames, Data: make([]*MetaData, 0)}
	data, _ := value.(model.Vector)
	for _, v := range data {
		t := &MetaData{Meta: map[string]string{
			COLLECTIONPOINTNUMS: "1",
		}, Data: make([]Point, 0)}
		for k, v := range v.Metric {
			t.Meta[string(k)] = string(v)
		}
		t.Data = append(t.Data, Point{Time: v.Timestamp.Unix(), Value: decimal(float64(v.Value))})
		res.Data = append(res.Data, t)
	}
	return res
}

func decimal(value float64) float64 {
	value, _ = strconv.ParseFloat(fmt.Sprintf("%.4f", value), 64)
	return value
}

func IsRate(metric string) bool {
	if _, ok := config.SC.Prometheus.PromQLRateTemplates[metric]; ok {
		return true
	}
	return false
}

func makeExpr(metric string, q *query.QueryOptions) string {
	var tmpl string
	if _, ok := config.SC.Prometheus.PromQLRateTemplates[metric]; ok {
		tmpl = config.SC.Prometheus.PromQLRateTemplates[metric]
	} else {
		tmpl = config.SC.Prometheus.PromQLTemplates[metric]
	}
	tmpl = strings.Replace(tmpl, "$1", q.NodeName, -1)
	tmpl = strings.Replace(tmpl, "$2", q.JobName, -1)
	tmpl = strings.Replace(tmpl, "$3", q.Namespace, -1)
	tmpl = strings.Replace(tmpl, "$4", q.ReponseCode, -1)
	tmpl = strings.Replace(tmpl, "$5", q.ModeName, -1)
	tmpl = strings.Replace(tmpl, "$6", q.StatisticalTime, -1)
	return tmpl
}

func ComputeRate(metrics []*Metric) []*Metric {
	var wg sync.WaitGroup
	var count int = 4
	for _, metric := range metrics {
		if IsRate(metric.MetricName) {
			for _, metaData := range metric.MetricData.Data {
				wg.Add(1)
				go func(metaData *MetaData) {
					defer wg.Done()
					var temp []Point
					var value float64
					for i := count; i < len(metaData.Data); i = i + 1 {
						if metaData.Data[i].Value == 0 {
							value = 0
						} else {
							value = (metaData.Data[i].Value - metaData.Data[i-1].Value) / (float64(count) * 15)
						}
						temp = append(temp, Point{Time: metaData.Data[i].Time, Value: decimal(math.Abs((value)))})
					}
					metaData.Data = temp
					metaData.Meta[CUSTOMCOMPUTERATE] = "true"
				}(metaData)
			}
		}
	}
	wg.Wait()
	return metrics
}

func AloneValueByRangeTime(metrics []*Metric) []*Metric {
	var wg sync.WaitGroup
	if len(metrics) == 0 {
		return metrics
	}
	for _, metric := range metrics {
		if metric.ErrorMes != "" {
			break
		}
		for _, metaData := range metric.MetricData.Data {
			wg.Add(1)
			go func(metaData *MetaData) {
				defer wg.Done()
				var index int
				for index = len(metaData.Data) - 1; index > 0; index-- {
					if metaData.Data[index].Value != 0 {
						break
					}
				}
				metaData.Data = []Point{{Time: metaData.Data[index].Time, Value: metaData.Data[index].Value}}
			}(metaData)
			metaData.Meta[ALONEVALUEBYRANGETIME] = "true"
		}
	}
	wg.Wait()
	return metrics
}

func fillZeroRangeTime(oldRes *MetricData, timeRange apiv1.Range) *MetricData {
	if len(oldRes.Data) == 0 {
		new := &MetaData{
			Meta: map[string]string{
				AllZERO:       "true",
				QUERYDURITION: timeRange.Step.String()},
			Data: make([]Point, 0),
		}
		for ti := timeRange.Start; ti.Before(timeRange.End.Add(timeRange.Step)); ti = ti.Add(timeRange.Step) {
			new.Data = append(new.Data, Point{ti.Unix(), 0})
		}
		new.Meta[TOTALCOUNT] = strconv.Itoa(len(new.Data))
		oldRes.Data = append(oldRes.Data, new)
		return oldRes
	}
	var wg sync.WaitGroup
	for _, old := range oldRes.Data {
		wg.Add(1)
		go func(old *MetaData) {
			defer wg.Done()
			new := make([]Point, 0)
			old.Meta[QUERYDURITION] = timeRange.Step.String()
			for ti := timeRange.Start; ti.Before(timeRange.End.Add(timeRange.Step)); ti = ti.Add(timeRange.Step) {
				new = append(new, Point{ti.Unix(), 0})
			}
			if len(new) != len(old.Data) {
				old.Meta[FILEZERO] = "true"
				for i := 0; i < len(new); i++ {
					for j := 0; j < len(old.Data); j++ {
						if new[i].Time == old.Data[j].Time {
							new[i].Value = decimal(old.Data[j].Value)
							break
						}
					}
				}

				old.Data = new
			}
			old.Meta[TOTALCOUNT] = strconv.Itoa(len(new))
		}(old)
	}
	wg.Wait()
	return oldRes
}

func fillZeroCurrentTime(oldRes *MetricData, currentTIme time.Time) *MetricData {
	if len(oldRes.Data) == 0 {
		newMetaData := &MetaData{
			Meta: map[string]string{
				FILEZERO:   "true",
				TOTALCOUNT: "1",
			},
			Data: []Point{
				{currentTIme.Unix(), 0},
			}}
		oldRes.Data = append(oldRes.Data, newMetaData)
		return oldRes
	}
	for _, old := range oldRes.Data {
		old.Meta[TOTALCOUNT] = "1"
		if len(old.Data) == 1 {
			continue
		}
		old.Data = append(old.Data, Point{currentTIme.Unix(), 0})
		old.Meta[FILEZERO] = "true"

	}
	return oldRes
}

func (p PrometheusServer) QueryCurrentTime(q query.QueryOptions, info map[string]string) []*Metric {
	var res []*Metric
	var mtx sync.Mutex
	var wg sync.WaitGroup
	metrics := strings.Split(q.MetricsName, "|")
	for _, metric := range metrics {
		querysql := makeExpr(metric, &q)
		wg.Add(1)
		go func(metric string, querysql string, info map[string]string, queryCurrentTime time.Time) {
			defer wg.Done()
			parsedResp := &Metric{MetricName: metric}

			value, _, err := p.client.Query(context.Background(), querysql, queryCurrentTime)
			if err != nil {
				log.Error(err)
				parsedResp.ErrorMes = err.Error()
			} else {
				parsedResp.MetricData = fillZeroCurrentTime(ParseQueryCurrentResp(value, info), queryCurrentTime)
			}
			mtx.Lock()
			res = append(res, parsedResp)
			mtx.Unlock()
		}(metric, querysql, info, q.Time)
	}
	wg.Wait()
	return res
}

func (p PrometheusServer) QueryRangeTime(q query.QueryOptions, info map[string]string) []*Metric {
	var res []*Metric
	var wg sync.WaitGroup
	var mtx sync.Mutex
	var timeRange apiv1.Range
	var epochStartTimeList []time.Time
	var epochEndTimeList []time.Time
	metrics := strings.Split(q.MetricsName, "|")
	epochStartTimeList, epochEndTimeList = GetTimeRangeList(q.Start, q.End, q.Step)
	for _, metric := range metrics {
		for index, startTime := range epochStartTimeList {
			timeRange = apiv1.Range{
				Start: startTime,
				End:   epochEndTimeList[index],
				Step:  q.Step,
			}
			querysql := makeExpr(metric, &q)
			wg.Add(1)
			go func(metric string, querysql string, info map[string]string, timeRange apiv1.Range, index int) {
				defer wg.Done()
				if IsRate(metric) {
					timeRange.Start = timeRange.Start.Add(-q.Step)
				}
				parsedResp := &Metric{MetricName: metric, MetricIndex: index}

				value, _, err := p.client.QueryRange(context.Background(), querysql, timeRange)
				if err != nil {
					log.Error(err)
					parsedResp.ErrorMes = err.Error()
				} else {
					parsedResp.MetricData = fillZeroRangeTime(ParseQueryRangeResp(value, info), timeRange)
				}
				mtx.Lock()
				res = append(res, parsedResp)
				mtx.Unlock()
			}(metric, querysql, info, timeRange, index)
		}
	}
	wg.Wait()
	// if len(epochStartTimeList) > 1 {
	// 	res = MergeMetric(res)
	// }
	return res
}

func (p PrometheusServer) NodeQueryRangeTime(q query.QueryOptions) []*Metric {

	info := map[string]string{
		"node_name": q.NodeName,
	}

	return p.QueryRangeTime(q, info)
}

func (p PrometheusServer) NodeQueryCurrentTime(q query.QueryOptions) []*Metric {

	info := map[string]string{
		"node_name": q.NodeName,
	}

	return p.QueryCurrentTime(q, info)
}

func (p PrometheusServer) JobQueryRangeTime(q query.QueryOptions) []*Metric {

	res := []*Metric{}
	jobNames := strings.Split(q.JobName, "|")
	for _, jobName := range jobNames {
		q.JobName = jobName
		info := map[string]string{
			"job_name": q.JobName,
		}
		res = append(res, p.QueryRangeTime(q, info)...)
	}
	return res
}

func (p PrometheusServer) ClusterQueryRangeTime(q query.QueryOptions) []*Metric {
	info := map[string]string{
		"cluster_name": "cluster",
	}
	return p.QueryRangeTime(q, info)
}

func (p PrometheusServer) ClusterQueryCurrentTime(q query.QueryOptions) []*Metric {

	info := map[string]string{
		"cluster_name": "cluster",
	}

	return p.QueryCurrentTime(q, info)
}

func (p PrometheusServer) ResourcePoolQueryRangeTime(q query.QueryOptions) []*Metric {
	rpNodes, _ := p.NodeNamesFromResourcePool(q.ResourcePoolName)
	var res []*Metric
	for rp, nodes := range rpNodes {
		info := map[string]string{
			"node_name":          strings.Join(nodes, "|"),
			"resource_pool_name": rp,
		}
		q.NodeName = strings.Join(nodes, "|")
		if q.NodeName == "" {
			continue
		}
		res = append(res, p.QueryRangeTime(q, info)...)

	}
	return res
}

func (p PrometheusServer) ResourcePoolQueryCurrentTime(q query.QueryOptions) []*Metric {
	var res []*Metric

	rpNodes, _ := p.NodeNamesFromResourcePool(q.ResourcePoolName)
	for rp, nodes := range rpNodes {
		info := map[string]string{
			"node_name":          strings.Join(nodes, "|"),
			"resource_pool_name": rp,
		}
		q.NodeName = strings.Join(nodes, "|")
		if q.NodeName == "" {
			continue
		}
		res = append(res, p.QueryCurrentTime(q, info)...)
	}

	return res
}

func (p PrometheusServer) NodeNamesFromResourcePool(resourcePoolName string) (map[string][]string, error) {
	var (
		group                         = "system.hero.ai"
		version                       = "v1alpha1"
		resources                     = "resourcepools"
		gvr                           = schema.GroupVersionResource{Group: group, Version: version, Resource: resources}
		res       map[string][]string = map[string][]string{}
	)

	rpNames := strings.Split(resourcePoolName, "|")
	for _, rpName := range rpNames {
		var resourcePool query.ResourcePool
		rp, err := p.dynamicClient.Resource(gvr).Get(context.TODO(), rpName, metav1.GetOptions{})
		if err != nil {
			log.Error(err.Error())
			continue
		}

		if err := runtime.DefaultUnstructuredConverter.FromUnstructured(rp.UnstructuredContent(), &resourcePool); err != nil {
			log.Error(err.Error())
			return nil, err
		}

		res[rpName] = resourcePool.Status.Nodes
	}

	return res, nil
}

type ClusterComponent struct {
	Components map[string]*Components `json:"components,omitempty" yaml:"components"`
}

type Components struct {
	ComponentDim string           `json:"componentDim,omitempty" yaml:"componentDim"`
	Component    []*ComponentInfo `json:"component,omitempty" yaml:"pods"`
}

type ComponentInfo struct {
	ComponentName string `json:"componentName" yaml:"componentName"`
	HealthStatus  int    `json:"healthStatus" yaml:"healthStatus"`
	Version       string `json:"version" yaml:"version"`
}

func getComponentDim(componentName string) string {
	if componentName == "karmada-system" {
		return "system"
	}
	if componentName == "node" {
		return "node"
	}
	return "cluster"
}

func (p PrometheusServer) ClusterNodeNameInfo() (*Components, error) {
	c := &Components{Component: make([]*ComponentInfo, 0), ComponentDim: "node"}
	nodes, err := p.clientSet.CoreV1().Nodes().List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		log.Error(err.Error())
		return nil, err
	}
	for _, node := range nodes.Items {
		p := &ComponentInfo{ComponentName: node.Name}
		if node.Status.Conditions[len(node.Status.Conditions)-1].Type == v1.NodeReady {
			p.HealthStatus = 1
		}
		c.Component = append(c.Component, p)
	}
	return c, nil
}

func (p PrometheusServer) ClusterPodInfo() (*ClusterComponent, error) {
	cc := &ClusterComponent{Components: map[string]*Components{}}
	var wg sync.WaitGroup
	var mux sync.Mutex
	pods, err := p.clientSet.CoreV1().Pods("").List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		log.Error(err.Error())
		return nil, err
	}

	for _, pod := range pods.Items {
		if pod.Namespace == "default" || pod.Namespace == "hero-user" {
			continue
		}
		wg.Add(1)
		go func(pod v1.Pod, cc *ClusterComponent) {
			wg.Done()
			ptemp := &ComponentInfo{ComponentName: pod.Name}
			if len(pod.Spec.Containers) == 1 {
				version := strings.Split(pod.Spec.Containers[0].Image, ":")
				ptemp.Version = version[len(version)-1]
			} else {
				ptemp.Version = "latest"
			}
			for _, containerStatuse := range pod.Status.ContainerStatuses {
				if containerStatuse.Ready {
					ptemp.HealthStatus = 1
					break
				}
			}
			mux.Lock()
			components, ok := cc.Components[pod.Namespace]
			if !ok {
				components = &Components{ComponentDim: getComponentDim(pod.Namespace), Component: make([]*ComponentInfo, 0)}
				cc.Components[pod.Namespace] = components
			}
			components.Component = append(components.Component, ptemp)
			mux.Unlock()
		}(pod, cc)
	}
	wg.Wait()
	nodeComponent, err := p.ClusterNodeNameInfo()
	if err != nil {
		log.Error(err.Error())
		return nil, err
	}
	cc.Components["node"] = nodeComponent
	return cc, nil
}

// func mapEqual(m1, m2 map[string]string) bool {
// 	for k, v := range m1 {
// 		if m2v, ok := m2[k]; !ok || v != m2v {
// 			return false
// 		}
// 	}
// 	return true
// }

// func MergeMetric(metrics []*Metric) []*Metric {
// 	var res []*Metric
// 	var count int
// 	for _, metric := range metrics {
// 		if metric.MetricIndex == 0 {
// 			res = append(res, metric)
// 		}
// 	}
// 	for _, head := range res {
// 		for _, metric := range metrics {
// 			if metric.MetricIndex == 0 || metric.MetricData != nil {
// 				continue
// 			}

// 			if metric.MetricName == head.MetricName && mapEqual(metric.MetricData.ServerName, head.MetricData.ServerName) {
// 				for m := 0; m < len(head.MetricData.Data); m++ {
// 					for n := 0; n < len(metric.MetricData.Data); n++ {
// 						_, ok1 := head.MetricData.Data[m].Meta[AllZERO]
// 						_, ok2 := metric.MetricData.Data[n].Meta[AllZERO]
// 						equal := reflect.DeepEqual(head.MetricData.Data[m].Meta, metric.MetricData.Data[n].Meta)
// 						if ok1 || ok2 || equal {
// 							head.MetricData.Data[m].Data = append(head.MetricData.Data[m].Data, metric.MetricData.Data[n].Data...)
// 							count++
// 						}
// 					}
// 					sort.Sort(ByTime(head.MetricData.Data[m].Data))
// 					head.MetricData.Data[m].Meta[MERGEMETRICSBATCHES] = strconv.Itoa(count)
// 				}
// 			}
// 		}
// 		count = 0
// 	}
// 	return res
// }
