package server

import (
	"hero-apiserver/internal/handler"
	"net/http"

	"github.com/emicklei/go-restful"
	log "github.com/sirupsen/logrus"
)

func (a *APIRouter) EventAddtoContainer() error {

	h, err := handler.NewEventHandler()
	if err != nil {
		log.Error(err.Error())
		return err
	}

	a.ws.Route(a.ws.GET("/events").
		To(h.QueryEvent).
		Doc("Query events against the cluster").
		Param(a.ws.QueryParameter("namespaces", "A comma-separated list of namespaces. This field restricts the query to specified namespaces. For example, the following filter matches the namespace my-ns and demo-ns: `my-ns,demo-ns`").DataType("string").DefaultValue("hero-user").Required(false)).
		Param(a.ws.QueryParameter("task_id", "Id of the task").DataType("string").Required(true)).
		Param(a.ws.QueryParameter("start_time", "Start time of query. Default to 0. The format is a string representing seconds since the epoch, eg. 1559664000.").DataType("string").Required(false)).
		Param(a.ws.QueryParameter("end_time", "End time of query. Default to now. The format is a string representing seconds since the epoch, eg. 1559664000.").DataType("string").Required(false)).
		Returns(http.StatusOK, StatusOK, nil)).
		Produces(restful.MIME_JSON, "text/plain")
	return nil
}
