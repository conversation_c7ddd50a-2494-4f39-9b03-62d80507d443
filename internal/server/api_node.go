package server

import (
	"net/http"

	"hero-apiserver/internal/handler"

	"github.com/emicklei/go-restful"
	log "github.com/sirupsen/logrus"
	"golang.org/x/crypto/ssh"
	"k8s.io/client-go/kubernetes"
)

func (a *APIRouter) NodeAddToContainer(k8sClient kubernetes.Interface, ssconfig *ssh.ClientConfig) error {
	h, err := handler.NewNodeHandler(k8sClient, ssconfig)
	if err != nil {
		log.Error(err.Error())
		return err
	}

	a.ws.Route(a.ws.GET("/platform/nodes").
		To(h.HandleListNodeForPlatform).
		Doc("Cluster level resources").
		Param(a.ws.QueryParameter("node_name", "name used to do filtering").Required(false)).
		Param(a.ws.QueryParameter("labelSelector", "labelSelector").Required(false)).
		Param(a.ws.QueryParameter("limit", "limit").Required(false)).
		Param(a.ws.QueryParameter("page", "page").Required(false).DataFormat("page=%d").DefaultValue("page=1")).
		Returns(http.StatusOK, StatusOK, nil).
		Produces(restful.MIME_JSON))

	a.ws.Route(a.ws.GET("/nodes").
		To(h.HandleListNode).
		Doc("Cluster level resources").
		Param(a.ws.QueryParameter("node_name", "name used to do filtering").Required(false)).
		Param(a.ws.QueryParameter("labelSelector", "labelSelector").Required(false)).
		Param(a.ws.QueryParameter("limit", "limit").Required(false)).
		Param(a.ws.QueryParameter("page", "page").Required(false).DataFormat("page=%d").DefaultValue("page=1")).
		Returns(http.StatusOK, StatusOK, nil).
		Produces(restful.MIME_JSON))

	a.ws.Route(a.ws.GET("/nodes/task").
		To(h.HandleListNodeTask).
		Doc("Cluster level resources").
		Param(a.ws.QueryParameter("node_name", "name used to do filtering").Required(false)).
		Param(a.ws.QueryParameter("labelSelector", "labelSelector").Required(false)).
		Param(a.ws.QueryParameter("limit", "limit").Required(false)).
		Param(a.ws.QueryParameter("page", "page").Required(false).DataFormat("page=%d").DefaultValue("page=1")).
		Returns(http.StatusOK, StatusOK, nil).
		Produces(restful.MIME_JSON))

	a.ws.Route(a.ws.POST("/nodes/{name}/{action}").
		To(h.HandleNodeAction).
		Doc("node isolation").
		Param(a.ws.PathParameter("name", "node name.").Required(true)).
		Param(a.ws.PathParameter("action", "node action option for isolation or recover.").Required(true)).
		Returns(http.StatusOK, StatusOK, nil).
		Produces(restful.MIME_JSON))

	a.ws.Route(a.ws.POST("/cards/{card}/{action}").
		To(h.HandleCardAction).
		Doc("cards isolation").
		Param(a.ws.PathParameter("card", "card id.").Required(true)).
		Param(a.ws.PathParameter("action", "card action option for isolation or recover.").Required(true)).
		Returns(http.StatusOK, StatusOK, nil).
		Produces(restful.MIME_JSON))

	return nil
}
