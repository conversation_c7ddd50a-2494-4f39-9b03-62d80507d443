package server

import (
	"net/http"

	"hero-apiserver/internal/handler"

	"github.com/emicklei/go-restful"
	log "github.com/sirupsen/logrus"
	"k8s.io/client-go/dynamic"
)

func (a *APIRouter) ResourcePoolAddToContainer(client dynamic.Interface) error {
	h, err := handler.NewResourcePoolHandler(client)
	if err != nil {
		log.Error(err.Error())
		return err
	}

	a.ws.Route(a.ws.GET("/resourcepool/{resourcepool}").
		To(h.HandleResourcePoolAction).
		Doc("Resource Pool Query by Name").
		Param(a.ws.PathParameter("resourcepool", "resourcepool name.").Required(true)).
		Returns(http.StatusOK, StatusOK, nil).
		Produces(restful.MIME_JSON))
	return nil
}
