package server

import (
	"net/http"

	"hero-apiserver/internal/common"
	"hero-apiserver/internal/handler"

	"github.com/emicklei/go-restful"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
)

func (a *APIRouter) GrafanaAddToContainer(karmadaClient dynamic.Interface, K8sclient kubernetes.Interface) error {
	h, _ := handler.NewGrafanaHandler(karmadaClient, K8sclient)
	a.ws.Route(a.ws.GET("").
		To(func(req *restful.Request, response *restful.Response) {
			for _, v := range a.apiResourceMap {
				a.apiresourceList.APIResources = append(a.apiresourceList.APIResources, v)
			}
			a.apiresourceList.GroupVersion = groupVersion.String()
			common.NewDefaultResponse().Succ(response, &a.apiresourceList)
		}).
		Returns(http.StatusOK, StatusOK, nil).
		Produces(restful.MIME_JSON))

	a.ws.Route(a.ws.GET("/grafana/datasource/create/{cluster_name}").
		To(h.HandleDatasourceCreate).
		Doc("Create Grafana DataSource.").
		Produces(restful.MIME_JSON))

	a.ws.Route(a.ws.GET("/grafana/datasource/delete/{cluster_name}").
		To(h.HandleDatasourceDelete).
		Doc("Delete Grafana DataSource.").
		Produces(restful.MIME_JSON))

	a.ws.Route(a.ws.GET("/grafana/datasource/list/{cluster_name}").
		To(h.HandleDatasourceIsExisted).
		Doc("Get Grafana DataSource.").
		Produces(restful.MIME_JSON))

	return nil
}
