package server

import (
	"hero-apiserver/internal/handler"
	"net/http"

	"github.com/emicklei/go-restful"
	log "github.com/sirupsen/logrus"
	"k8s.io/client-go/kubernetes"
	"volcano.sh/apis/pkg/client/clientset/versioned"
	"volcano.sh/volcano/pkg/scheduler/conf"
)

func (a *APIRouter) TaskAddToContainer(k8sClient kubernetes.Interface, volcanoClient *versioned.Clientset, schedulerConf *conf.SchedulerConfiguration) error {
	h, err := handler.NewTaskHandler(k8sClient, volcanoClient, schedulerConf)
	if err != nil {
		log.Error(err.Error())
		return err
	}

	a.ws.Route(a.ws.POST("/task/allocatable").
		To(h.HandleQueryFreeResource).
		Doc("query free resource").
		Returns(http.StatusOK, StatusOK, nil).
		Produces(restful.MIME_JSON))

	return nil

}
