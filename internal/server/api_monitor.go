package server

import (
	"net/http"

	"hero-apiserver/internal/common"
	"hero-apiserver/internal/handler"

	"github.com/emicklei/go-restful"
	log "github.com/sirupsen/logrus"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
)

func NewWebService(gv schema.GroupVersion) *restful.WebService {
	webservice := restful.WebService{}
	webservice.Path(APIRootPath + "/" + gv.String()).
		Produces(restful.MIME_JSON)
	return &webservice
}

func (a *APIRouter) MonitoringAddToContainer(dynamicClient dynamic.Interface, clientSet kubernetes.Interface) error {
	h, err := handler.NewMonitorHandler(dynamicClient, clientSet)
	if err != nil {
		log.Error(err.Error())
		return err
	}

	a.ws.Route(a.ws.GET("").
		To(func(req *restful.Request, response *restful.Response) {
			for _, v := range a.apiResourceMap {
				a.apiresourceList.APIResources = append(a.apiresourceList.APIResources, v)
			}
			a.apiresourceList.GroupVersion = groupVersion.String()
			common.NewDefaultResponse().Succ(response, &a.apiresourceList)
		}).
		Returns(http.StatusOK, StatusOK, nil).
		Produces(restful.MIME_JSON))

	a.ws.Route(a.ws.GET("/gpucard/recover/{node_name}/{uuid}").
		To(h.HandleGPURecoverQuery).
		Doc("Get cluster-level metric data.").
		Param(a.ws.PathParameter("node_name", "Node name.").DataType("string").Required(true)).
		Param(a.ws.PathParameter("uuid", "Node name.").DataType("string").Required(true)).
		Produces(restful.MIME_JSON))

	a.ws.Route(a.ws.GET("/gpucard/list/{node_name}").
		To(h.HandleGPUListQuery).
		Doc("Get cluster-level metric data.").
		Param(a.ws.PathParameter("node_name", "Node name.").DataType("string").Required(true)).
		Produces(restful.MIME_JSON))

	a.ws.Route(a.ws.GET("/gpucard/lock/{node_name}/{uuid}").
		To(h.HandleGPULockQuery).
		Doc("Get cluster-level metric data.").
		Param(a.ws.PathParameter("node_name", "Node name.").DataType("string").Required(true)).
		Param(a.ws.PathParameter("uuid", "Node name.").DataType("string").Required(true)).
		Produces(restful.MIME_JSON))

	a.ws.Route(a.ws.POST("/resource_pool").
		To(h.HandleResourcePoolQuery).
		Doc("Get cluster-level metric data.").
		Param(a.ws.QueryParameter("time", "Current time of query. ").DataType("string").Required(false)).
		Param(a.ws.BodyParameter("resource_pool_namme", "RP Name.").DataType("string").Required(false)).
		Produces(restful.MIME_JSON))

	a.ws.Route(a.ws.GET("/cluster").
		To(h.HandleClusterQuery).
		Doc("Get cluster-level metric data.").
		Param(a.ws.QueryParameter("metrics_name", "Metrics name").DataType("string").Required(true)).
		Param(a.ws.PathParameter("mode_name", "gpu card type.").DataType("string").Required(false)).
		Param(a.ws.QueryParameter("start", "Start time of query. ").DataType("string").Required(false)).
		Param(a.ws.QueryParameter("time", "Current time of query. ").DataType("string").Required(false)).
		Param(a.ws.PathParameter("namespace", "Namespace.").DataType("string").Required(false)).
		Param(a.ws.QueryParameter("end", "End time of query. ").DataType("string").Required(false)).
		Param(a.ws.QueryParameter("step", "Time interval.").DataType("string").DefaultValue("1m").Required(false)).
		Produces(restful.MIME_JSON))

	a.ws.Route(a.ws.GET("/cluster/component").
		To(h.HandleClusterComponentQuery).
		Doc("Get cluster-level metric data.").
		Produces(restful.MIME_JSON))

	a.ws.Route(a.ws.GET("/node/{node_name}").
		To(h.HandleNodeQuery).
		Doc("Get node-level metric data of the specific node.").
		Param(a.ws.PathParameter("node_name", "Node name.").DataType("string").Required(true)).
		Param(a.ws.QueryParameter("metrics_name", "Metrics name").DataType("string").Required(true)).
		Param(a.ws.PathParameter("mode_name", "gpu card type.").DataType("string").Required(false)).
		Param(a.ws.PathParameter("namespace", "Namespace.").DataType("string").Required(false)).
		Param(a.ws.QueryParameter("start", "Start time of query.").DataType("string").Required(false)).
		Param(a.ws.QueryParameter("end", "End time of query. ").DataType("string").Required(false)).
		Param(a.ws.QueryParameter("time", "Current time of query. ").DataType("string").Required(false)).
		Param(a.ws.QueryParameter("step", "Time interval.").DataType("string").DefaultValue("1m").Required(false)).
		Produces(restful.MIME_JSON))

	a.ws.Route(a.ws.GET("/jobs/{job_name}").
		To(h.HandleJobQuery).
		Doc("Get job-level metric data of the specific node.").
		Param(a.ws.PathParameter("job_name", "Job name.").DataType("string").Required(true)).
		Param(a.ws.PathParameter("response_code", "Response code.").DataType("string").Required(false)).
		Param(a.ws.PathParameter("mode_name", "gpu card type.").DataType("string").Required(false)).
		Param(a.ws.PathParameter("task_type", "Task name.").DataType("string").Required(false)).
		Param(a.ws.PathParameter("namespace", "Namespace.").DataType("string").Required(false)).
		Param(a.ws.QueryParameter("metrics_name", "Metrics name").DataType("string").Required(true)).
		Param(a.ws.QueryParameter("start", "Start time of query.").DataType("string").Required(false)).
		Param(a.ws.QueryParameter("end", "End time of query.").DataType("string").Required(false)).
		Param(a.ws.QueryParameter("step", "Time interval.").DataType("string").DefaultValue("1m").Required(false)).
		Produces(restful.MIME_JSON))
	return nil
}

func getAPIResourceList() *metav1.APIResourceList {
	return &metav1.APIResourceList{
		TypeMeta: metav1.TypeMeta{
			Kind:       "APIResourceList",
			APIVersion: "v1",
		},

		APIResources: []metav1.APIResource{},
	}

}
