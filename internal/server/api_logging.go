package server

import (
	"hero-apiserver/internal/handler"
	"net/http"

	"github.com/apache/rocketmq-client-go/v2"
	"github.com/emicklei/go-restful"
	"github.com/minio/minio-go/v7"
	log "github.com/sirupsen/logrus"
)

func (a *APIRouter) LoggingAddToContainer(minioS3 *minio.Client, minioFS *minio.Client, rocketmq *rocketmq.Producer) error {
	h, err := handler.NewLogHandler(minioS3, minioFS, rocketmq)
	if err != nil {
		log.Error(err.Error())
		return err
	}

	a.ws.Route(a.ws.GET("/logs").
		To(h.QueryLogs).
		Doc("Query logs against the cluster.").
		Param(a.ws.QueryParameter("namespaces", "A comma-separated list of namespaces. This field restricts the query to specified namespaces. For example, the following filter matches the namespace my-ns and demo-ns: `my-ns,demo-ns`").DataType("string").Required(false)).
		Param(a.ws.QueryParameter("pods", "A comma-separated list of pods. This field restricts the query to specified pods. For example, the following filter matches the pod my-po and demo-po: `my-po,demo-po`").DataType("string").Required(true)).
		Param(a.ws.QueryParameter("containers", "A comma-separated list of containers. This field restricts the query to specified container. For example, the following filter matches the pod my-po and demo-po: `container-1,container-2`").DataType("string").Required(false)).
		Param(a.ws.QueryParameter("start_time", "Start time of query. Default to 0. The format is a string representing seconds since the epoch, eg. 1559664000.").DataType("string").Required(false)).
		Param(a.ws.QueryParameter("end_time", "End time of query. Default to now. The format is a string representing seconds since the epoch, eg. 1559664000.").DataType("string").Required(false)).
		Param(a.ws.QueryParameter("sort", "Sort order. One of asc, desc. This field sorts logs by timestamp.").DataType("string").DefaultValue("desc").Required(false)).
		Param(a.ws.QueryParameter("show_time", "Show logs time. One of true, false. This field shows logs by choose.").DataType("string").DefaultValue("true").Required(false)).
		Param(a.ws.QueryParameter("from", "The offset from the result set. This field returns query results from the specified offset. It requires **operation** is set to query. Defaults to 0 (i.e. from the beginning of the result set).").DataType("integer").DefaultValue("0").Required(false)).
		Param(a.ws.QueryParameter("size", "Size of result to return. It requires **operation** is set to query. Defaults to 10 (i.e. 10 log records).").DataType("integer").DefaultValue("10").Required(false)).
		Param(a.ws.QueryParameter("keyword_filter", "A keyword  of log query. This field restricts the query to specified logs. For example, the following filter matches the logs: `error`").DataType("string").Required(false)).
		Returns(http.StatusOK, StatusOK, nil)).
		Produces(restful.MIME_JSON, "text/plain")

	a.ws.Route(a.ws.GET("/logs/export").
		To(h.LogsExport).
		Doc("Query logs against the cluster.").
		Param(a.ws.QueryParameter("namespaces", "A comma-separated list of namespaces. This field restricts the query to specified namespaces. For example, the following filter matches the namespace my-ns and demo-ns: `my-ns,demo-ns`").DataType("string").Required(false)).
		Param(a.ws.QueryParameter("pods", "A comma-separated list of pods. This field restricts the query to specified pods. For example, the following filter matches the pod my-po and demo-po: `my-po,demo-po`").DataType("string").Required(false)).
		Param(a.ws.QueryParameter("containers", "A comma-separated list of containers. This field restricts the query to specified container. For example, the following filter matches the pod my-po and demo-po: `container-1,container-2`").DataType("string").Required(false)).
		Param(a.ws.QueryParameter("start_time", "Start time of query. Default to 0. The format is a string representing seconds since the epoch, eg. 1559664000.").DataType("string").Required(false)).
		Param(a.ws.QueryParameter("end_time", "End time of query. Default to now. The format is a string representing seconds since the epoch, eg. 1559664000.").DataType("string").Required(false)).
		Param(a.ws.QueryParameter("sort", "Sort order. One of asc, desc. This field sorts logs by timestamp.").DataType("string").DefaultValue("desc").Required(false)).
		Param(a.ws.QueryParameter("show_time", "Show logs time. One of true, false. This field shows logs by choose.").DataType("string").DefaultValue("true").Required(false)).
		Param(a.ws.QueryParameter("from", "The offset from the result set. This field returns query results from the specified offset. It requires **operation** is set to query. Defaults to 0 (i.e. from the beginning of the result set).").DataType("integer").DefaultValue("0").Required(false)).
		Param(a.ws.QueryParameter("size", "Size of result to return. It requires **operation** is set to query. Defaults to 10 (i.e. 10 log records).").DataType("integer").DefaultValue("10").Required(false)).
		Param(a.ws.QueryParameter("keyword_filter", "A keyword  of log query. This field restricts the query to specified logs. For example, the following filter matches the logs: `error`").DataType("string").Required(false)).
		Returns(http.StatusOK, StatusOK, nil)).
		Produces(restful.MIME_JSON, "text/plain")

	return nil
}
