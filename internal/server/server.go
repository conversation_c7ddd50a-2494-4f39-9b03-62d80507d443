package server

import (
	"context"
	"net/http"
	"time"

	"golang.org/x/crypto/ssh"
	"volcano.sh/apis/pkg/client/clientset/versioned"

	"hero-apiserver/internal/config"

	"github.com/apache/rocketmq-client-go/v2"
	"github.com/emicklei/go-restful"
	"github.com/minio/minio-go/v7"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
	urlruntime "k8s.io/apimachinery/pkg/util/runtime"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"volcano.sh/volcano/pkg/scheduler/conf"
)

const (
	APIRootPath        = "/apis"
	StatusOK           = "ok"
	MimeMergePatchJSON = "application/merge-patch+json"
	MimeJSONPatchJSON  = "application/json-patch+json"
	GroupName          = "monitoring.hero.ai"
)

var (
	groupVersion = schema.GroupVersion{Group: GroupName, Version: "v1"}
)

type APIServer struct {
	srv              *http.Server
	container        *restful.Container
	dynamicClient    dynamic.Interface
	karmadaClient    dynamic.Interface
	k8sClient        kubernetes.Interface
	volcanoClientset *versioned.Clientset
	minioS3Client    *minio.Client
	minioFSClient    *minio.Client
	rocketmqClient   *rocketmq.Producer
	schedulerConf    *conf.SchedulerConfiguration
	sshConf          *ssh.ClientConfig
}

type APIRouter struct {
	ws              *restful.WebService
	apiresourceList *metav1.APIResourceList
	apiResourceMap  map[string]metav1.APIResource
}

func init() {
	restful.RegisterEntityAccessor(MimeMergePatchJSON, restful.NewEntityAccessorJSON(restful.MIME_JSON))
	restful.RegisterEntityAccessor(MimeJSONPatchJSON, restful.NewEntityAccessorJSON(restful.MIME_JSON))
}

func NewAPIRouter() *APIRouter {
	return &APIRouter{
		apiResourceMap:  make(map[string]metav1.APIResource),
		apiresourceList: getAPIResourceList(),
		ws:              NewWebService(groupVersion),
	}
}

func (s *APIServer) Serve(httpConfig config.TLSConfig) error {
	//handler初始化
	s.container = restful.NewContainer()
	s.container.Router(restful.CurlyRouter{})

	s.installAPIs()
	s.srv.Handler = s.container
	return s.srv.ListenAndServeTLS(httpConfig.Cert, httpConfig.Key)
}

func Setup(address string, k8sclient kubernetes.Interface, karmadaClientSet dynamic.Interface, volcanoclient *versioned.Clientset, dynamicClient dynamic.Interface, minioS3Client *minio.Client, minioFSClient *minio.Client, rocketmqClient *rocketmq.Producer, schedulerConf *conf.SchedulerConfiguration, sshConf *ssh.ClientConfig) (*APIServer, error) {
	s := &APIServer{
		k8sClient:        k8sclient,
		dynamicClient:    dynamicClient,
		karmadaClient:    karmadaClientSet,
		volcanoClientset: volcanoclient,
		srv: &http.Server{
			Addr:         address,
			WriteTimeout: 60 * time.Second,
			ReadTimeout:  30 * time.Second,
		},
		minioS3Client:  minioS3Client,
		minioFSClient:  minioFSClient,
		rocketmqClient: rocketmqClient,
		schedulerConf:  schedulerConf,
		sshConf:        sshConf,
	}

	return s, nil
}

func (s *APIServer) installAPIs() {
	//注册api
	route := NewAPIRouter()
	urlruntime.Must(route.LoggingAddToContainer(s.minioS3Client, s.minioFSClient, s.rocketmqClient))
	urlruntime.Must(route.MonitoringAddToContainer(s.dynamicClient, s.k8sClient))
	urlruntime.Must(route.NodeAddToContainer(s.k8sClient, s.sshConf))
	urlruntime.Must(route.GrafanaAddToContainer(s.karmadaClient, s.k8sClient))
	urlruntime.Must(route.TaskAddToContainer(s.k8sClient, s.volcanoClientset, s.schedulerConf))
	urlruntime.Must(route.ResourcePoolAddToContainer(s.dynamicClient))
	urlruntime.Must(route.EventAddtoContainer())
	urlruntime.Must(route.InfoAddtoContainer())

	s.container.Add(route.ws)
}

func (s *APIServer) Close() error {
	if s == nil {
		return nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	return s.srv.Shutdown(ctx)
}
