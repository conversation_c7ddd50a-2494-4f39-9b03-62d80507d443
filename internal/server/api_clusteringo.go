package server

import (
	"hero-apiserver/internal/handler"
	"net/http"

	"github.com/emicklei/go-restful"
	log "github.com/sirupsen/logrus"
)

func (a *APIRouter) InfoAddtoContainer() error {

	h, err := handler.NewProductId()
	if err != nil {
		log.Error(err.Error())
		return err
	}

	a.ws.Route(a.ws.GET("/info").
		To(h.QueryId).
		Doc("Query events against the cluster").
		Returns(http.StatusOK, StatusOK, nil)).
		Produces(restful.MIME_JSON, "text/plain")
	return nil
}
