package utils

import (
	"os"
	"reflect"
	"sort"
	"strings"
	"testing" // 或者是正确的路径，例如 "github.com/username/repository/internal/utils"
)

func TestDiff(t *testing.T) {
	base := []string{"a", "b", "c", "d"}
	exclude := []string{"b", "d"}
	expected := []string{"a", "c"}
	result := Diff(base, exclude)
	sort.Strings(result)
	sort.Strings(expected)

	if !reflect.DeepEqual(result, expected) {
		t.<PERSON>("Expected %v, but got %v", expected, result)
	}
}
func TestUnique(t *testing.T) {
	input := []string{"a", "b", "a", "c", "b"}
	expected := []string{"c", "b", "a"}

	// 排序输入和预期切片
	result := Unique(input)
	sort.Strings(result)
	sort.Strings(expected)

	// 比较排序后的切片
	if !reflect.DeepEqual(result, expected) {
		t.<PERSON>("Expected %v, but got %v", expected, result)
	}
}

func TestCamelCaseToUnderscore(t *testing.T) {
	input := "CamelCaseString"
	expected := "camel_case_string"
	result := CamelCaseToUnderscore(input)

	if result != expected {
		t.Errorf("Expected %s, but got %s", expected, result)
	}
}

func TestUnderscoreToCamelCase(t *testing.T) {
	input := "underscore_string"
	expected := "UnderscoreString"
	result := UnderscoreToCamelCase(input)

	if result != expected {
		t.Errorf("Expected %s, but got %s", expected, result)
	}
}

func TestFindString(t *testing.T) {
	array := []string{"a", "b", "c"}
	str := "b"
	expected := 1
	result := FindString(array, str)

	if result != expected {
		t.Errorf("Expected %d, but got %d", expected, result)
	}
}

func TestStringIn(t *testing.T) {
	array := []string{"a", "b", "c"}
	str := "b"
	expected := true
	result := StringIn(str, array)

	if result != expected {
		t.Errorf("Expected %t, but got %t", expected, result)
	}
}

func TestReverse(t *testing.T) {
	input := "hello"
	expected := "olleh"
	result := Reverse(input)

	if result != expected {
		t.Errorf("Expected %s, but got %s", expected, result)
	}
}

func TestSplit(t *testing.T) {
	input := "a,b,c"
	sep := ","
	expected := []string{"a", "b", "c"}
	result := Split(input, sep)

	if !reflect.DeepEqual(result, expected) {
		t.Errorf("Expected %v, but got %v", expected, result)
	}
}

func TestStripAnsi(t *testing.T) {
	input := "\u001B[31mHello\u001B[0m"
	expected := "Hello"
	result := StripAnsi(input)

	if result != expected {
		t.Errorf("Expected %s, but got %s", expected, result)
	}
}

func TestShortenString(t *testing.T) {
	input := "hello world"
	n := 5
	expected := "hello"
	result := ShortenString(input, n)

	if result != expected {
		t.Errorf("Expected %s, but got %s", expected, result)
	}
}

func TestNewTLSConfig(t *testing.T) {
	// Create temporary files for the CA, cert, and key.
	caFile, _ := os.CreateTemp("", "caCertFile.pem")
	tlsCertFile, _ := os.CreateTemp("", "tlsCertFile.pem")
	tlsKeyFile, _ := os.CreateTemp("", "tlsKeyFile.key")
	defer os.Remove(caFile.Name())
	defer os.Remove(tlsCertFile.Name())
	defer os.Remove(tlsKeyFile.Name())

	// Write test data to the files.
	caFile.WriteString(`-----BEGIN CERTIFICATE-----
MIID4zCCAsugAwIBAgIUWConmHOClHHgwN3xa6RPQEKcSdowDQYJKoZIhvcNAQEL
BQAwgYAxCzAJBgNVBAYTAmNuMQ4wDAYDVQQIDAVhbmh1aTEOMAwGA1UEBwwFaGVm
ZWkxDzANBgNVBAoMBmxlaW5hbzEPMA0GA1UECwwGc3lzdGVtMQ0wCwYDVQQDDARs
eW9uMSAwHgYJKoZIhvcNAQkBFhF3dWxpYW5nQGxlaW5hby5haTAeFw0yNDExMjUw
MzI3NTZaFw0yNTExMjUwMzI3NTZaMIGAMQswCQYDVQQGEwJjbjEOMAwGA1UECAwF
YW5odWkxDjAMBgNVBAcMBWhlZmVpMQ8wDQYDVQQKDAZsZWluYW8xDzANBgNVBAsM
BnN5c3RlbTENMAsGA1UEAwwEbHlvbjEgMB4GCSqGSIb3DQEJARYRd3VsaWFuZ0Bs
ZWluYW8uYWkwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDzWRPe1k8j
QF9zJYU/145GBUcXeiFD3ksH9j4yeF595n9QfiyR5qZAXC00RoUBbw5J7VRfl/y3
GedpYr1BPT78Lt1IIl/5y0CkV3WMGHECK4InSQSbns/4+SOJUVQsCTF6nxoBU+79
/i715XuMWP2l2Da0OkQlkM1kOyVMqZcsw1WEg0MngyFh1Eq2Y+k0BDcg3igrVnet
Dkp6krmDZfcS0eWEfqyijiwq4Cd5bjURrCmvBMovbBPNJvopdqYi7wZMk0FCSF0p
zG253VgRqRoh9ey+U1jxbnrwuU9OoWSuGfEmrGfulBBZw4N9yAfw3BjE392HZCOS
mFkHJOXWpE9tAgMBAAGjUzBRMB0GA1UdDgQWBBScNxGHp0Cm8l84EH+UYfIe/bIP
sDAfBgNVHSMEGDAWgBScNxGHp0Cm8l84EH+UYfIe/bIPsDAPBgNVHRMBAf8EBTAD
AQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBY3FUZ8RW5cAdIZ9xVWG31yWN/h1waDkeS
QtFgjEjQPjAAfXn/GzGnZ9Kgb2SZafbdlhTICo72ElQoDMP2NulglG9qQjYEz5dE
oTN4KTAi79WDLReeMav/3cbe7AQ8irCD2gT+OluesLUfj4PHOvxEStTceWVaQ4xI
ZM9S7vKZYnMTOUAZlgdtRxEkKsvZXrps79B4IaJfdVbykQQGf3BVKGUttvgll3e9
1uDwXryd0hduOI0t/ym2m9roYJyNtWla/yFT2KY086NfJB4pxiO00i/KYY0Zi1hI
neh8Z5OVDeAJWT4oVA8o/MoRPDkzP5RVi8+nLCKPqZnSUt0KG9mt
-----END CERTIFICATE-----`)
	tlsCertFile.WriteString(`-----BEGIN CERTIFICATE-----
MIIDiTCCAnECFD7KFtQexpYa728sTT5o2AzUrF2ZMA0GCSqGSIb3DQEBCwUAMIGA
MQswCQYDVQQGEwJjbjEOMAwGA1UECAwFYW5odWkxDjAMBgNVBAcMBWhlZmVpMQ8w
DQYDVQQKDAZsZWluYW8xDzANBgNVBAsMBnN5c3RlbTENMAsGA1UEAwwEbHlvbjEg
MB4GCSqGSIb3DQEJARYRd3VsaWFuZ0BsZWluYW8uYWkwHhcNMjQxMTI1MDMyOTEz
WhcNMjUxMTI1MDMyOTEzWjCBgDELMAkGA1UEBhMCQ04xDjAMBgNVBAgMBWFuaHVp
MQ4wDAYDVQQHDAVoZWZlaTEPMA0GA1UECgwGbGVpbmFvMQ8wDQYDVQQLDAZzeXN0
ZW0xDTALBgNVBAMMBGx5b24xIDAeBgkqhkiG9w0BCQEWEXd1bGlhbmdAbGVpbmFv
LmFpMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxe1YMseXd0H+kc8b
mD5FKNzvjy2u2J35qxnCErxp2eFZTxRLvSR2g+7Hv9bbhvZF9jzpQImDbbD9UST8
IRFbemaH40k6qI/grP8MPSwSCkWPSpxHmZZrGSQ5cTT9WIaOV5zWmV4XiDVL4raS
Pz7g6Vi+Oim3enH8B2HXhdzJfwy0dqFEdI7yUbr0Gtm7U0SYOQ4/tzQXgnrH6Wud
zKyJxyz5N5TKRY2i2ouMEU1927cVSzDpKBq+b/pil/6fWWbcQNqQWvuki9bwcHDx
m5BRBB+2fbtCEykmsCCN3ECW1JJVeUUW7e+gUx+Q4CgqRK7DcV4FCYuhYQLVXjf7
hCMOHwIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQB6nV77nOqG2unRWsa1674GMINN
5UpmERjQNQzDXqK1aGw29PCwAb6uZu67ajeQyCFO7rLQ/5g/Vxq9q83i9UA3sX8W
MGj5wy1MzSFo+wsNT0vFNNS9g52hjRM/q05bLs4qACzX6mR83fx0GQLZtq+xBBTc
B2/8LPetduAOnu80Zxs4xmmCJjD3jnt3i8i+k6ppEW9PxVETEDr6xFu9RSGT9zow
CEJkaZMVwVD2rAMEVFc9yz3GBHM1lhu8LSHom7Sy/LkbxjBBNqqLybpOWUyYMHOp
BQUIPde8Zv/9ruNUy/UGXeDh7Q8YashD8i3rC5n6FsylnQfBTZUU9Ib/Q8F0
-----END CERTIFICATE-----`)
	tlsKeyFile.WriteString(`***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`)

	tlsConfig, err := NewTLSConfig(caFile.Name(), tlsCertFile.Name(), tlsKeyFile.Name())

	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
	if tlsConfig == nil {
		t.Errorf("Expected non-nil tlsConfig")
	}
	if len(tlsConfig.Certificates) == 0 {
		t.Errorf("Expected certificates in tlsConfig")
	}
	if tlsConfig.RootCAs == nil {
		t.Errorf("Expected RootCAs in tlsConfig")
	}
}

func TestGetFunctionName(t *testing.T) {
	funcToTest := TestGetFunctionName
	expected := "utils.TestGetFunctionName"
	result := GetFunctionName(funcToTest)

	if !strings.Contains(result, expected) {
		t.Errorf("Expected function name to contain %s, but got %s", expected, result)
	}
}
