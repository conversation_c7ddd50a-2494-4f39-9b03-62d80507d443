//nolint:all
package logging

import (
	"fmt"
	"hero-apiserver/internal/common"

	"hero-apiserver/internal/model"
	"sort"
	"strconv"
	"time"

	log "github.com/sirupsen/logrus"
)

const (
	QUERYRANGE = "/loki/api/v1/query_range"
	QUERY      = "/loki/api/v1/query"
)

// use httpclient
type LokiClient struct {
	host string
	c    common.Client
}

func NewLokiClient(host string, htc common.Client) (LokiClient, error) {
	var err error
	loki := &LokiClient{
		host: host,
		c:    htc,
	}

	return *loki, err
}

func (c *LokiClient) Search(query map[string]string, scope bool) (*model.StructResp, error) {

	var endpoint string
	if scope {
		endpoint = QUERYRANGE
	} else {
		endpoint = QUERY
	}
	result := &model.Response{}
	_, err := c.c.Get(c.host, endpoint, nil, nil, result, query)
	if err != nil {
		return nil, err
	}

	return model.ParseResponse(result)
}

func (c *LokiClient) SearchRange(queryStr string, limit int64, start, end time.Time, direction string, step, interval string) (*model.LogResult, error) {
	result := &model.LogResult{}

	var (
		lokiResponse = &model.Response{}
		batchSize    = model.DefaultBatchSize
		resultLength = model.DefaultBatchSize
		total        = int64(0)
	)

	var err error
	if limit < batchSize {
		batchSize = limit
	}
	log.Info("start search SearchRange,time is: ", time.Now().String())
	var lastEntry = make([]*model.Entry, 0)
	for total < limit {
		bs := batchSize
		if limit-total < batchSize {
			bs = limit - total + int64(len(lastEntry))
		}

		resultLength, lastEntry, err = c.queryAndStream(result, lastEntry, lokiResponse, queryStr, bs, start, end, direction, step, interval)
		if err != nil {
			return nil, fmt.Errorf("query failed: %+v", err)
		}

		if stopCheckFunc(resultLength, lastEntry, limit) {
			break
		}
		if int64(len(lastEntry)) >= batchSize {
			return nil, fmt.Errorf("invalid batch size %v", batchSize)
		}
		total += resultLength
		if direction == "asc" {
			start = lastEntry[0].Timestamp
		} else {
			end = lastEntry[0].Timestamp.Add(1 * time.Nanosecond)
		}
		if resultLength < batchSize-1 {
			break
		}
	}

	if total < limit || len(lastEntry) == 0 {
		return result, nil
	}

	log.Info("end search SearchRange,time is: ", time.Now().String())
	return result, nil
}

func (c *LokiClient) SearchFullRange(queryStr string, limit int64, start, end time.Time, direction string, step, interval string) (*model.LogResult, error) {
	var (
		lokiResponse = &model.Response{}
		resultLength = model.DefaultBatchSize
		lastEntry    = make([]*model.Entry, 0)
		result       = &model.LogResult{}
	)
	var err error
	log.Info("start search SearchRange,time is: ", time.Now().String())

	for start.Before(end) {
		resultLength, lastEntry, err = c.queryAndStream(result, lastEntry, lokiResponse, queryStr, model.DefaultBatchSize, start, end, direction, step, interval)
		if err != nil {
			return nil, fmt.Errorf("query failed: %+v", err)
		}

		if resultLength < model.DefaultBatchSize-1 || resultLength == 0 {
			break
		}

		if direction == "asc" {
			start = lastEntry[0].Timestamp
		} else {
			end = lastEntry[0].Timestamp.Add(1 * time.Nanosecond)
		}
	}

	log.Info("end search SearchRange,time is: ", time.Now().String())
	return result, nil
}

func (c *LokiClient) queryAndStream(result *model.LogResult, lastEntry []*model.Entry, lokiResponse *model.Response,
	queryStr string, bs int64, start, end time.Time, direction string, step, interval string) (resultLength int64, Entry []*model.Entry, err error) {

	params := c.BuilderQuery(queryStr, bs, start, end, direction, step, interval)
	response, err := c.doQuery(QUERYRANGE, lokiResponse, params)
	if err != nil {
		return resultLength, nil, fmt.Errorf("query failed: %+v", err)
	}
	switch lokiResponse.Data.ResultType {
	case model.ResultTypeStream:
		resultLength, Entry = c.printStream(direction, response.Streams, result, lastEntry)
	}

	return resultLength, Entry, nil
}

func (c *LokiClient) doQuery(endpoint string, lokiResponse *model.Response, query map[string]string) (*model.StructResp, error) {
	_, err := c.c.Get(c.host, endpoint, nil, nil, lokiResponse, query)
	if err != nil {
		return nil, fmt.Errorf("query loki err: %v", err)
	}
	lokiResults, err := model.ParseResponse(lokiResponse)
	if err != nil {
		return nil, err
	}
	return lokiResults, nil
}

func (c *LokiClient) BuilderQuery(queryStr string, limit int64, start, end time.Time, direction string, step, interval string) map[string]string {

	query := map[string]string{
		model.LIMIT:     strconv.FormatInt(limit, 10),
		model.DIRECTION: "BACKWARD",
		model.END:       strconv.FormatInt(end.UnixNano(), 10),
	}
	if direction == "asc" {
		query[model.DIRECTION] = "FORWARD"
	}
	if !start.IsZero() {
		query[model.START] = strconv.FormatInt(start.UnixNano(), 10)
	}

	if interval != "" {
		query[model.INTERVAL] = interval
	}

	if step != "" {
		query[model.STEP] = step
	}

	query[model.QUERYSTR] = queryStr

	return query
}

func (c *LokiClient) printStream(direction string, streams model.Streams, out *model.LogResult, lastEntry []*model.Entry) (int64, []*model.Entry) {
	// sort and display entries
	allEntries := make([]model.StreamEntryPair, 0)
	for _, s := range streams {
		for _, e := range s.Values {
			allEntries = append(allEntries, model.StreamEntryPair{
				Entry:  e,
				LabelS: s.Labels,
			})
		}
	}
	if len(allEntries) == 0 {
		return 0, nil
	}

	if direction == "asc" {
		sort.Slice(allEntries, func(i, j int) bool { return allEntries[i].Entry.Timestamp.Before(allEntries[j].Entry.Timestamp) })
	} else {
		sort.Slice(allEntries, func(i, j int) bool { return allEntries[i].Entry.Timestamp.After(allEntries[j].Entry.Timestamp) })
	}

	printed := int64(0)
	for _, e := range allEntries {
		if len(lastEntry) > 0 && e.Entry.Timestamp == lastEntry[0].Timestamp {
			skip := false
			for _, le := range lastEntry {
				if e.Entry.Line == le.Line {
					skip = true
				}
			}
			if skip {
				continue
			}
		}
		if len(e.Entry.Line) != 0 {
			out.Logs = append(out.Logs, e.Entry)
		}
		printed++
	}

	lel := []*model.Entry{}
	// Start with the timestamp of the last entry
	le := allEntries[len(allEntries)-1].Entry
	for i, e := range allEntries {
		// Save any entry which has this timestamp (most of the time this will only be the single last entry)
		if e.Entry.Timestamp.Equal(le.Timestamp) {
			lel = append(lel, &allEntries[i].Entry)
		}
	}

	return printed, lel
}

func stopCheckFunc(resultLength int64, lastEntry []*model.Entry, limit int64) bool {
	// Was not a log stream query, or no results, no more batching
	if resultLength <= 0 {
		return true
	}
	// Also no result, wouldn't expect to hit this.
	if len(lastEntry) == 0 {
		return true
	}
	// Can only happen if all the results return in one request
	if resultLength == limit {
		return true
	}

	return false
}
