package logging

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"hero-apiserver/internal/model"
)

func TestBuildEventQueryString(t *testing.T) {
	tests := []struct {
		name    string
		filter  EventSearchFilter
		want    string
		wantErr bool
	}{
		{
			name: "Valid filter with namespace and event",
			filter: EventSearchFilter{
				NamespaceFilter: []string{"namespace1", "namespace2"},
				EventFilter:     []string{"event1", "event2"},
			},
			want:    `{namespace=~"namespace1|namespace2", event=~"event1|event2"}` + model.DefaultPatterns1,
			wantErr: false,
		},
		{
			name: "Valid filter with only namespace",
			filter: EventSearchFilter{
				NamespaceFilter: []string{"namespace1"},
			},
			want:    `{namespace=~"namespace1"}` + model.DefaultPatterns1,
			wantErr: false,
		},

		{
			name:    "Invalid filter with no namespace",
			filter:  EventSearchFilter{},
			wantErr: true,
		},
		{
			name: "Valid filter with empty event",
			filter: EventSearchFilter{
				NamespaceFilter: []string{"namespace1"},
				EventFilter:     []string{},
			},
			want:    `{namespace=~"namespace1"}` + model.DefaultPatterns1,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := buildEventQueryString(tt.filter)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}
		})
	}
}
