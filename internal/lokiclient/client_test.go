package logging

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"
	"time"

	"hero-apiserver/internal/common"
	"hero-apiserver/internal/model"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

type MockClient struct {
	mock.Mock
}

func (m *MockClient) Get(base string, endpoint string, headers http.Header, basicAuth *common.BasicAuth, responseStruct interface{}, querystring map[string]string) (*http.Response, error) {
	args := m.Called(base, endpoint, headers, basicAuth, responseStruct, querystring)
	return args.Get(0).(*http.Response), args.Error(1)
}

func (m *MockClient) Post(base string, endpoint string, headers http.Header, basicAuth *common.BasicAuth, payload io.Reader, responseStruct interface{}, querystring map[string]string) (*http.Response, error) {
	args := m.Called(base, endpoint, headers, basicAuth, payload, responseStruct, querystring)
	return args.Get(0).(*http.Response), args.Error(1)
}

func (m *MockClient) Do(ar *common.APIRequest, responseStruct interface{}, options ...interface{}) (*http.Response, error) {
	args := m.Called(ar, responseStruct, options)
	return args.Get(0).(*http.Response), args.Error(1)
}

// 实现 http.RoundTripper 接口
func (m *MockClient) RoundTrip(req *http.Request) (*http.Response, error) {
	args := m.Called(req)

	// 假设你返回一个模拟的响应
	return args.Get(0).(*http.Response), args.Error(1)
}

// 测试 BuilderQuery 函数
func TestBuilderQuery(t *testing.T) {
	lokiClient := LokiClient{
		host: "http://localhost",
	}

	// 设置测试参数
	queryStr := "{job=\"test-job\"}"
	limit := int64(100)
	start := time.Now().Add(-1 * time.Hour)
	end := time.Now()
	direction := "asc"
	step := "15s"
	interval := "30s"

	// 调用 BuilderQuery 函数
	query := lokiClient.BuilderQuery(queryStr, limit, start, end, direction, step, interval)

	// 断言生成的查询参数正确
	assert.Equal(t, query["query"], queryStr)
	assert.Equal(t, query["limit"], "100")
	assert.Equal(t, query["direction"], "FORWARD")
	assert.Equal(t, query["start"], strconv.FormatInt(start.UnixNano(), 10))
	assert.Equal(t, query["end"], strconv.FormatInt(end.UnixNano(), 10))
	assert.Equal(t, query["step"], step)
	assert.Equal(t, query["interval"], interval)
}

func TestStopCheckFunc(t *testing.T) {
	tests := []struct {
		name           string
		resultLength   int64
		lastEntry      []*model.Entry
		limit          int64
		expectedResult bool
	}{
		{
			name:           "No results returned (resultLength <= 0)",
			resultLength:   0,
			lastEntry:      []*model.Entry{{Timestamp: time.Now(), Line: "Log1"}},
			limit:          10,
			expectedResult: true,
		},
		{
			name:           "No last entry (len(lastEntry) == 0)",
			resultLength:   5,
			lastEntry:      []*model.Entry{}, // 空的 lastEntry 列表
			limit:          10,
			expectedResult: true,
		},
		{
			name:           "Result length equals limit (resultLength == limit)",
			resultLength:   10,
			lastEntry:      []*model.Entry{{Timestamp: time.Now(), Line: "Log1"}},
			limit:          10,
			expectedResult: true,
		},
		{
			name:           "Valid case where result length is less than limit",
			resultLength:   5,
			lastEntry:      []*model.Entry{{Timestamp: time.Now(), Line: "Log1"}},
			limit:          10,
			expectedResult: false,
		},
		{
			name:           "No results and no entries (resultLength <= 0, len(lastEntry) == 0)",
			resultLength:   0,
			lastEntry:      []*model.Entry{},
			limit:          10,
			expectedResult: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 调用 stopCheckFunc
			result := stopCheckFunc(tt.resultLength, tt.lastEntry, tt.limit)
			assert.Equal(t, tt.expectedResult, result)
		})
	}
}

func TestNewLokiClient(t *testing.T) {

	host := "***********:3100"
	htc := common.NewHTTPClient()

	lc, err := NewLokiClient(host, htc)

	assert.NoError(t, err)
	assert.NotNil(t, lc)
}

func TestPrintStream(t *testing.T) {
	// 创建一个模拟的 LokiClient
	lokiClient := LokiClient{
		host: "http://localhost",
		c:    common.NewHTTPClient(),
	}

	// 模拟数据：时间戳从大到小
	streams := model.Streams{
		{
			Labels: map[string]string{"label1": "value1"},
			Values: []model.Entry{
				{Timestamp: time.Now(), Line: "Log entry 2"},
				{Timestamp: time.Now().Add(time.Second * 5), Line: "Log entry 1"},
			},
		},
	}

	// 模拟结果对象
	result := &model.LogResult{}

	// 调用 printStream 方法
	printed, entries := lokiClient.printStream("desc", streams, result, nil)

	assert.Equal(t, int64(2), printed)
	assert.NotEqual(t, int64(0), len(entries))
}
func TestSearch(t *testing.T) {
	// 创建一个 MockClient 实例
	mockClient := new(MockClient)

	// 设置 Mock 的 RoundTrip 方法的返回值
	mockClient.On("RoundTrip", mock.Anything).Return(&http.Response{
		StatusCode: http.StatusOK,
		Body:       io.NopCloser(bytes.NewReader([]byte(`{"status":"success","data":{"result":[{"metric":{"__name__":"up","instance":"localhost:9090"},"value":["1625247600","1"]}]}}`))),
	}, nil)

	// 创建一个 HTTPClient 并使用 MockClient
	client := &common.HTTPClient{
		Client: &http.Client{Transport: mockClient},
	}

	// 设置查询参数
	query := map[string]string{"job": "test-job"}

	// 调用 Get 方法
	result, err := client.Get("http://localhost", "/api/v1/query", nil, nil, nil, query)

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, result.StatusCode)

}
func TestSearchRange(t *testing.T) {
	// 创建一个模拟的 HTTP 服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 返回模拟的 JSON 响应
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		fmt.Fprintf(w, `{
			"status": "success",
			"data": {
				"resultType": "streams",
				"result": [
					{
						"stream": {
							"label1": "value1"
						},
						"values": [
							["1625247600", "Log entry 1"],
							["1625247660", "Log entry 2"]
						]
					}
				]
			}
		}`)
	}))
	defer server.Close()

	// 创建 LokiClient 实例，并将其指向模拟服务器
	lokiClient := &LokiClient{
		host: server.URL, // 使用测试服务器的 URL
		c:    common.NewHTTPClient(),
	}

	// 设置时间范围
	start := time.Now().Add(-1 * time.Hour)
	end := time.Now()

	// 设置查询参数
	queryStr := "{job=\"test-job\"}"
	limit := int64(10)
	direction := "asc"
	step := "15s"
	interval := "30s"

	// 调用 SearchRange 方法
	result, err := lokiClient.SearchRange(queryStr, limit, start, end, direction, step, interval)

	// 验证没有错误
	assert.NoError(t, err)

	fmt.Println("==> ", result.Logs)
	assert.Equal(t, "Log entry 1", result.Logs[0].Line)
}
