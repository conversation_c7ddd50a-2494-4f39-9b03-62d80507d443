package logging

import (
	"fmt"
	"testing"
	"time"

	"hero-apiserver/internal/model"

	"github.com/stretchr/testify/assert"
)

func TestBuildQueryString(t *testing.T) {
	tests := []struct {
		name        string
		sf          model.SearchFilter
		expectedStr string
		expectedErr bool
	}{
		{
			name: "Valid NamespaceFilter",
			sf: model.SearchFilter{
				NamespaceFilter: []string{"namespace1", "namespace2"},
				Keywords:        "error",
			},
			expectedStr: `{namespace=~"namespace1|namespace2"}|~"error"`,
			expectedErr: false,
		},
		{
			name: "Valid PodFilter",
			sf: model.SearchFilter{
				PodFilter: []string{"pod1", "pod2"},
				Keywords:  "error",
			},
			expectedStr: `{pod=~"pod1|pod2"}|~"error"`,
			expectedErr: false,
		},
		{
			name: "Valid ContainerFilter",
			sf: model.SearchFilter{
				ContainerFilter: []string{"container1", "container2"},
				Keywords:        "crash",
			},
			expectedStr: `{container=~"container1|container2"}|~"crash"`,
			expectedErr: false,
		},
		{
			name: "All filters with Keywords",
			sf: model.SearchFilter{
				NamespaceFilter: []string{"namespace1", "namespace2"},
				PodFilter:       []string{"pod1", "pod2"},
				ContainerFilter: []string{"container1", "container2"},
				Keywords:        "error",
			},
			expectedStr: `{namespace=~"namespace1|namespace2", pod=~"pod1|pod2", container=~"container1|container2"}|~"error"`,
			expectedErr: false,
		},
		{
			name: "No filters and empty Keywords",
			sf: model.SearchFilter{
				Keywords: "",
			},
			expectedStr: "",
			expectedErr: true,
		},
		{
			name: "No keywords with filters",
			sf: model.SearchFilter{
				NamespaceFilter: []string{"namespace1"},
				PodFilter:       []string{"pod1"},
				ContainerFilter: []string{"container1"},
				Keywords:        "error",
			},
			expectedStr: `{namespace=~"namespace1", pod=~"pod1", container=~"container1"}|~"error"`,

			expectedErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行 buildQueryString 函数
			queryStr, err := buildQueryString(tt.sf)

			fmt.Println("queryStr++>", queryStr)

			if tt.expectedErr {
				assert.Error(t, err)
				assert.Empty(t, queryStr)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedStr, queryStr)
			}
		})
	}
}

type MockLokiClient struct {
	// 模拟 LokiClient 的行为
}

func (m *MockLokiClient) SearchRange(queryStr string, size int64, startTime, endTime time.Time, sort, step, interval string) (*model.LogResult, error) {
	// 返回模拟的响应数据
	return &model.LogResult{
		Logs: []model.Entry{
			{
				Line:      "Log entry 1",
				Timestamp: time.Now(),
			},
			{
				Line:      "Log entry 2",
				Timestamp: time.Now(),
			},
		},
	}, nil
}
