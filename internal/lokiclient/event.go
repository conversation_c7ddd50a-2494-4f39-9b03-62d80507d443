//nolint:all
package logging

import (
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"hero-apiserver/internal/model"

	log "github.com/sirupsen/logrus"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type Line struct {
	Id              string      `json:"id"`
	JobId           string      `json:"jobId"`
	Type            string      `json:"type"`
	Reason          string      `json:"reason"`
	Message         string      `json:"message"`
	CreateTime      metav1.Time `json:"createTime"`
	ResourceVersion string      `json:"resourceVersion"`
}

type EventSearchFilter struct {
	NamespaceFilter []string
	EventFilter     []string
	Sort            string
	Size            int64
	StartTime       time.Time
	EndTime         time.Time
}

func (c *Logclient) SearchEvents(sf EventSearchFilter) ([]Line, error) {
	queryStr, err := buildEventQueryString(sf)
	if err != nil {
		return nil, err
	}
	resp, err := c.c.SearchRange(queryStr, sf.<PERSON>, sf.StartTime, sf.EndTime, sf.Sort, "", "")
	if err != nil {
		return nil, err
	}

	if resp == nil || len(resp.Logs) == 0 {
		return nil, err
	}

	log.Info("resp Content: " + resp.Logs[0].Line)
	log.Info("resp Timestamp: " + strconv.FormatInt(resp.Logs[0].Timestamp.UnixNano(), 10))

	var eventList []Line
	var eventMap = make(map[string]struct{})
	var reasonMap = make(map[string]Line) // 存储事件的完整结构体

	for _, log := range resp.Logs {
		var event Line
		if err := json.Unmarshal([]byte(log.Line), &event); err != nil {
			return nil, err
		}
		if _, ok := eventMap[event.Id]; !ok {
			eventMap[event.Id] = struct{}{}
			key := event.Message + event.Reason
			//排除掉推理事件Reson去重 Application
			if strings.Contains(event.Message, "Application") {
				reasonMap[key+event.CreateTime.String()] = event
			} else {
				if existingEvent, found := reasonMap[key]; !found || event.CreateTime.Time.Before(existingEvent.CreateTime.Time) {
					reasonMap[key] = event
				}
			}
		}
	}
	for _, event := range reasonMap {
		eventList = append(eventList, event)
	}
	sort.Slice(eventList, func(i, j int) bool {
		return eventList[i].CreateTime.Time.After(eventList[j].CreateTime.Time)
	})

	return customSortEvents(eventList) //customSortPreserveOrder(eventList, items), nil
}

func customSortEvents(events []Line) ([]Line, error) {
	allResourceVersionEmpty := true
	for _, event := range events {
		if event.ResourceVersion != "" {
			allResourceVersionEmpty = false
			break
		}
	}

	if allResourceVersionEmpty {
		return events, nil
	}

	sort.SliceStable(events, func(i, j int) bool {
		if events[i].ResourceVersion != "" && events[j].ResourceVersion != "" {
			return events[i].ResourceVersion > events[j].ResourceVersion
		}
		return false
	})
	return events, nil
}

// func customSortPreserveOrder(input []Line, order []string) []Line {
// 	orderMap := make(map[string]int)
// 	for i, val := range order {
// 		orderMap[val] = i + 1
// 	}

// 	type element struct {
// 		index int
// 		line  Line
// 	}
// 	var toSort []element
// 	var others []element

// 	for i, val := range input {
// 		if _, exists := orderMap[val.Reason]; exists {
// 			toSort = append(toSort, element{i, val})
// 		} else {
// 			others = append(others, element{i, val})
// 		}
// 	}

// 	sort.Slice(toSort, func(i, j int) bool {
// 		return orderMap[toSort[i].line.Reason] < orderMap[toSort[j].line.Reason]
// 	})

// 	result := make([]Line, 0)

// 	for _, el := range others {
// 		result = append(result, el.line)
// 	}

// 	for _, el := range toSort {
// 		result = append(result, el.line)
// 	}

// 	return result
// }

// func (c *Logclient) buildQueryString
func buildEventQueryString(sf EventSearchFilter) (string, error) {
	if len(sf.NamespaceFilter) == 0 {
		return "", fmt.Errorf("invalid parameter req: %v", sf)
	}

	var queryStr string

	var nsList string

	var queryList []string

	var eventList string

	if len(sf.NamespaceFilter) != 0 {

		for _, ns := range sf.NamespaceFilter {
			nsList = nsList + ns + "|"
		}

		nsList = fmt.Sprintf(`namespace=~"%s"`, strings.TrimSuffix(nsList, "|"))
		queryList = append(queryList, nsList)
	}

	if len(sf.EventFilter) != 0 {
		for _, event := range sf.EventFilter {
			eventList = eventList + event + "|"
		}
		eventList = fmt.Sprintf(`event=~"%s"`, strings.TrimSuffix(eventList, "|"))
		queryList = append(queryList, eventList)
	}

	queryStr = "{" + strings.Join(queryList, ", ") + "}" + model.DefaultPatterns1

	log.Info("queryStr: " + queryStr)

	return queryStr, nil
}
