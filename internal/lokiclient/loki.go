package logging

import (
	"fmt"
	"io"
	"time"

	log "github.com/sirupsen/logrus"

	"hero-apiserver/internal/common"

	// loki "hero-apiserver/internal/client/lokiclient"
	"hero-apiserver/internal/model"
	"strconv"
	"strings"
)

type Source struct {
	Log        string `json:"log"`
	Time       string `json:"time"`
	Kubernetes `json:"kubernetes"`
}

type Kubernetes struct {
	Namespace string `json:"namespace_name"`
	Pod       string `json:"pod_name"`
	Container string `json:"container_name"`
	Host      string `json:"host"`
}

// handle logQL
type Logclient struct {
	c LokiClient
}

func NewLogClient(host string, htc common.Client) (*Logclient, error) {

	client := &Logclient{}

	var err error
	client.c, err = NewLokiClient(host, htc)
	return client, err
}

func (c *Logclient) SearchLogs(sf model.SearchFilter, _, _ int64, _ string) (model.Logs, error) {

	queryStr, err := buildQueryString(sf)
	if err != nil {
		return model.Logs{}, err
	}
	resp, err := c.c.SearchRange(queryStr, sf.Size, sf.Starttime, sf.Endtime, sf.Sort, "", "")

	if err != nil {
		return model.Logs{}, err
	}

	if resp == nil || len(resp.Logs) == 0 {
		return model.Logs{}, nil
	}

	log.Info("resp Content: " + resp.Logs[0].Line)
	log.Info("resp Timestamp: " + strconv.FormatInt(resp.Logs[0].Timestamp.UnixNano(), 10))

	l := model.Logs{}
	for _, logDetail := range resp.Logs {
		if logDetail.Line != "" {

			l.Records = append(l.Records, model.Record{

				Log:       logDetail.Line,
				Timestamp: logDetail.Timestamp.UnixNano(),
				Namespace: "",
				Container: "",
			})
		}
	}

	l.Total = int64(len(l.Records))
	return l, nil
}

func (c *Logclient) ExportLogs(sf model.SearchFilter, w io.Writer) (model.Logs, error) {
	var err error
	var resp *model.LogResult
	queryStr, err := buildQueryString(sf)
	if err != nil {
		return model.Logs{}, fmt.Errorf("logs querystr err: %v", err)
	}

	// 使用 Logclient 中的方法进行日志搜索
	if sf.Size == 0 {
		resp, err = c.c.SearchFullRange(queryStr, sf.Size, sf.Starttime, sf.Endtime, sf.Sort, "", "")
	} else {
		resp, err = c.c.SearchRange(queryStr, sf.Size, sf.Starttime, sf.Endtime, sf.Sort, "", "")
	}
	if err != nil {
		return model.Logs{}, fmt.Errorf("failed to search logs: %v", err)
	}

	if resp == nil || len(resp.Logs) == 0 {
		return model.Logs{}, nil
	}

	log.Info("resp Content: " + resp.Logs[0].Line)
	log.Info("resp Timestamp: " + strconv.FormatInt(resp.Logs[0].Timestamp.UnixNano(), 10))

	l := model.Logs{}
	for _, logDetail := range resp.Logs {
		if logDetail.Line != "" {

			l.Records = append(l.Records, model.Record{

				Log:       logDetail.Line,
				Timestamp: logDetail.Timestamp.UnixNano(),
				Namespace: "",
				Container: "",
			})
		}
	}

	l.Total = int64(len(l.Records))

	// 将搜索到的日志内容写入 io.Writer 接口
	if sf.Sort == "desc" {
		//倒序写入 todo
		for i := len(resp.Logs) - 1; i >= 0; i-- {
			logDetail := resp.Logs[i]
			logstr := logDetail.Line
			if sf.Showtime == "true" {
				logstr = fmt.Sprintf("%s: %s", logDetail.Timestamp.UTC().Add(8*time.Hour).Format("2006-01-02 15:04:05"), logDetail.Line)

			}
			if _, err := fmt.Fprint(w, logstr); err != nil {
				return model.Logs{}, fmt.Errorf("failed to write log: %v", err)
			}
		}
	} else {
		for _, logDetail := range resp.Logs {
			logstr := logDetail.Line
			if sf.Showtime == "true" {
				logstr = fmt.Sprintf("%s: %s", logDetail.Timestamp.UTC().Add(8*time.Hour).Format("2006-01-02 15:04:05"), logDetail.Line)

			}
			if _, err := fmt.Fprint(w, logstr); err != nil {
				return model.Logs{}, fmt.Errorf("failed to write log: %v", err)
			}
		}
	}

	return l, nil
}

func buildQueryString(sf model.SearchFilter) (string, error) {
	if len(sf.NamespaceFilter) == 0 && len(sf.PodFilter) == 0 && len(sf.ContainerFilter) == 0 {
		return "", fmt.Errorf("invalid parameter req: %v", sf)
	}

	var podList string
	var queryStr string

	var nsList string

	var containerList string

	var queryList []string

	if len(sf.NamespaceFilter) != 0 {

		for _, ns := range sf.NamespaceFilter {
			nsList = nsList + ns + "|"
		}

		nsList = fmt.Sprintf(`namespace=~"%s"`, strings.TrimSuffix(nsList, "|"))
		queryList = append(queryList, nsList)
	}

	if len(sf.PodFilter) != 0 {
		for _, pod := range sf.PodFilter {
			podList = podList + pod + "|"
		}
		podList = fmt.Sprintf(`pod=~"%s"`, strings.TrimSuffix(podList, "|"))
		queryList = append(queryList, podList)
	}
	if len(sf.ContainerFilter) != 0 {
		for _, container := range sf.ContainerFilter {
			containerList = containerList + container + "|"
		}
		containerList = fmt.Sprintf(`container=~"%s"`, strings.TrimSuffix(containerList, "|"))
		queryList = append(queryList, containerList)
	}

	queryStr = "{" + strings.Join(queryList, ", ") + "}" + model.DefaultPatterns1

	if len(queryStr) > 0 && sf.Keywords != "" {
		// queryStr = fmt.Sprintf(`%s |~ "(?i)%s"`, queryStr, sf.Keywords)
		queryStr = fmt.Sprintf(`%s|~"%s"`, queryStr, sf.Keywords)

	}

	log.Info("queryStr: " + queryStr)

	return queryStr, nil
}
