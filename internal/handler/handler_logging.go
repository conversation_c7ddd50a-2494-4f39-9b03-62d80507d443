package handler

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"hero-apiserver/internal/common"
	"hero-apiserver/internal/config"
	logging "hero-apiserver/internal/lokiclient"
	"hero-apiserver/internal/model"
	"hero-apiserver/internal/utils"

	"github.com/apache/rocketmq-client-go/v2"
	"github.com/apache/rocketmq-client-go/v2/primitive"
	"github.com/emicklei/go-restful"
	"github.com/minio/minio-go/v7"
	log "github.com/sirupsen/logrus"
	"k8s.io/klog"
)

type Loghandler struct {
	client   logging.Logclient
	minioS3  minio.Client
	minioFS  minio.Client
	rocketmq rocketmq.Producer
}

type FileMsg struct {
	CopyID string `json:"copyID,omitempty" `
	Reason string `json:"reason,omitempty" `
	Status string `json:"status,omitempty" `
	Mess   Mess   `json:"message,omitempty" `
}

type Mess struct {
	Total int64 `json:"total,omitempty" `
}

func NewLogHandler(minioS3 *minio.Client, minioFS *minio.Client, rocketmq *rocketmq.Producer) (*Loghandler, error) {

	//要有"http://"
	loggingClient, err := logging.NewLogClient(config.SC.GetLokiAddress(), common.NewHTTPClient())

	if err != nil {
		return nil, err
	}

	return &Loghandler{
		client:   *loggingClient,
		minioS3:  *minioS3,
		minioFS:  *minioFS,
		rocketmq: *rocketmq,
	}, nil
}

func (th *Loghandler) QueryLogs(req *restful.Request, response *restful.Response) {
	query, err := ParseQueryParameter(req)

	if err != nil {
		log.Errorln(err)
		common.NewDefaultResponse().Fail(response, err)
		return
	}

	sf, err := CheckQueryParameter(query, model.QueryLimits)

	if err != nil {
		log.Error(err)
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	result, err := th.client.SearchLogs(sf, query.From, query.Size, query.Sort)

	if err != nil {
		log.Error(err)
		common.NewDefaultResponse().Fail(response, err)
		return

	}
	// log.Info(fmt.Sprintf("\n=====> QueryLogs finished in %dms", time.Since(startTime)/time.Millisecond)) //Segment finished in xxms
	common.NewDefaultResponse().Succ(response, &model.ListResult{Items: result.Records, TotalItems: int(result.Total)})
}

func (th *Loghandler) LogsFullExport(req *restful.Request, response *restful.Response) {
	query, err := ParseQueryParameter(req)
	if err != nil {
		log.Errorln(err)
		common.NewDefaultResponse().Fail(response, err)
		return
	}

	sf, err := CheckQueryParameter(query, model.ExportLimits)
	if err != nil {
		log.Error(err)
		common.NewDefaultResponse().Fail(response, err)
		return
	}

	go func(query *model.LogQueryReq) {
		var buf bytes.Buffer
		msg := &FileMsg{
			CopyID: query.TaskID,
			Status: "Success",
			Reason: "upload file successed",
			Mess:   Mess{},
		}
		result, err := th.client.ExportLogs(sf, &buf)
		if err != nil {
			msg.Status = "Error"
			msg.Reason = err.Error()
			log.Errorln(err)
			if err := th.ProduceMsg(config.SC.RocketMq.Topic, msg); err != nil {
				log.Errorln(err)
			}
			return
		}
		msg.Mess.Total = result.Total
		bucketFileName := query.BucketFilePath + "/" + query.PodFilter + ".txt"
		reader := bytes.NewReader(buf.Bytes())
		if query.StorageType == "s3" {
			_, err = th.minioS3.PutObject(context.Background(), query.BucketName, bucketFileName, reader, int64(len(buf.Bytes())),
				minio.PutObjectOptions{ContentType: "application/octet-stream"})
		} else {
			_, err = th.minioFS.PutObject(context.Background(), query.BucketName, bucketFileName, reader, int64(len(buf.Bytes())),
				minio.PutObjectOptions{ContentType: "application/octet-stream"})
		}
		if err != nil {
			msg.Status = "Error"
			msg.Reason = err.Error()
			log.Error(err)
		}
		if err := th.ProduceMsg(config.SC.RocketMq.Topic, msg); err != nil {
			log.Error(err)
		}
	}(query)

	common.NewDefaultResponse().Succ(response, common.DefaultResponse{
		Msg:  "Success",
		Code: 200,
	})
	response.WriteHeader(http.StatusOK)
}

func (th *Loghandler) LogsExport(req *restful.Request, response *restful.Response) {
	var buf bytes.Buffer

	query, err := ParseQueryParameter(req)
	if err != nil {
		log.Errorln(err)
		common.NewDefaultResponse().Fail(response, err)
		return
	}

	sf, err := CheckQueryParameter(query, model.ExportLimits)
	if err != nil {
		log.Error(err)
		common.NewDefaultResponse().Fail(response, err)
		return
	}

	// 调用 LogClient 的 ExportLogs 方法导出日志，写入 buf
	_, err = th.client.ExportLogs(sf, &buf)
	if err != nil {
		log.Errorln(err)
		common.NewDefaultResponse().Fail(response, err)
		return
	}

	// 设置响应头，指定文件名和 Content-Disposition 为 attachment 类型
	filename := "exported_logs.txt"
	response.Header().Set("Content-Disposition", "attachment; filename="+filename)
	response.Header().Set("Content-Type", "text/plain")
	response.Header().Set("Content-Length", fmt.Sprint(len(buf.Bytes())))

	// 将 buf 中的内容写入 response.ResponseWriter，完成文件下载
	_, err = response.ResponseWriter.Write(buf.Bytes())
	if err != nil {
		log.Errorln(err)
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	// 成功导出日志，设置响应状态码为 200
	response.WriteHeader(http.StatusOK)

}

func CheckQueryParameter(query *model.LogQueryReq, limits int64) (model.SearchFilter, error) {
	// if query.Size <= 0 || query.Size > limits {
	// 	query.Size = limits
	// }

	if query.StartTime.After(query.EndTime) {
		query.StartTime = query.EndTime.AddDate(0, 0, -10)
	}

	if query.ShowTime != "false" {
		query.ShowTime = "true"
	}
	if query.Sort == "" {
		query.Sort = "desc"
	}

	sf := model.SearchFilter{
		NamespaceFilter: utils.Split(query.NamespaceFilter, ","),
		PodFilter:       utils.Split(query.PodFilter, ","),
		ContainerFilter: utils.Split(query.ContainerFilter, ","),
		Keywords:        query.KeywordFilter,
		Starttime:       query.StartTime,
		Endtime:         query.EndTime,
		Showtime:        query.ShowTime,
		Sort:            query.Sort,
		Size:            query.Size,
	}
	return sf, nil
}

func ParseQueryParameter(req *restful.Request) (*model.LogQueryReq, error) {
	var q model.LogQueryReq
	q.StorageType = req.QueryParameter("storage_type")
	q.NamespaceFilter = req.QueryParameter("namespaces")
	q.BucketName = req.QueryParameter("bucket_name")
	q.BucketFilePath = req.QueryParameter("bucket_file_path")
	q.TaskID = req.QueryParameter("task_id")
	q.PodFilter = req.QueryParameter("pods")
	q.ContainerFilter = req.QueryParameter("containers")
	q.KeywordFilter = req.QueryParameter("keyword_filter")
	var err error
	q.StartTime, err = timestamp2Time(req.QueryParameter("start_time"), time.Now().AddDate(0, 0, -10))
	if err != nil {
		return nil, err
	}

	q.EndTime, err = timestamp2Time(req.QueryParameter("end_time"), time.Now())
	if err != nil {
		return nil, err
	}

	q.From, _ = strconv.ParseInt(req.QueryParameter("from"), 10, 64)
	q.Size, _ = strconv.ParseInt(req.QueryParameter("size"), 10, 64)

	q.Sort = req.QueryParameter("sort")
	q.ShowTime = req.QueryParameter("show_time")
	return &q, nil
}

func timestamp2Time(timeStamp string, defaultTime time.Time) (time.Time, error) {
	if timeStamp == "" || len(timeStamp) == 0 {
		return defaultTime, nil
	}

	sec, err := strconv.ParseInt(timeStamp, 10, 64)
	if err != nil {
		return defaultTime, err
	}
	newSec := sec / int64(time.Second)
	nSec := sec % int64(time.Second)
	return time.Unix(newSec, nSec), nil
}

func Marshal(data interface{}) []byte {
	marshal, err := json.Marshal(data)
	if err != nil {
		klog.Errorf("json marshal failed")
		return []byte{}
	}
	return marshal
}

func (th *Loghandler) ProduceMsg(topic string, data interface{}) error {
	msg := primitive.NewMessage(topic, Marshal(data))
	res, err := th.rocketmq.SendSync(context.Background(), msg)
	if err != nil {
		klog.Errorf("Send message error: %s\n", err)
		return err
	}
	klog.Infof("Send message success: result=%s\n", res.String())
	return nil
}
