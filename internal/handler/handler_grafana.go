package handler

import (
	"context"
	"fmt"
	"hero-apiserver/internal/common"
	"hero-apiserver/internal/config"
	"strings"
	"time"

	"github.com/emicklei/go-restful"
	"github.com/go-resty/resty/v2"
	log "github.com/sirupsen/logrus"
	"golang.org/x/exp/rand"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
)

type GrafanaHandler struct {
	karmadaClient dynamic.Interface
	K8sclient     kubernetes.Interface
}

type GrafanaDataSource struct {
	Name        string `json:"name"`
	Type        string `json:"type"`
	URL         string `json:"url"`
	Access      string `json:"access"`
	BasicAuth   bool   `json:"basicAuth"`
	UID         string `json:"uid"`
	ID          int    `json:"id"`
	OrgID       int    `json:"orgId"`
	TypeName    string `json:"typeName"`
	TypeLogoURL string `json:"typeLogoUrl"`
	User        string `json:"user"`
	Database    string `json:"database"`
	IsDefault   bool   `json:"isDefault"`
	JSONData    any    `json:"jsonData"`
	ReadOnly    bool   `json:"readOnly"`
}

func NewGrafanaHandler(karmadaClient dynamic.Interface, K8sclient kubernetes.Interface) (*GrafanaHandler, error) {
	return &GrafanaHandler{karmadaClient: karmadaClient, K8sclient: K8sclient}, nil
}

func (h *GrafanaHandler) HandleDatasourceIsExisted(req *restful.Request, response *restful.Response) {
	gds := []GrafanaDataSource{}
	client := resty.New()
	clusterName := req.PathParameter("cluster_name")
	url := fmt.Sprintf("http://%s:%s/grafana/api/datasources", config.SC.Grafana.Addr, config.SC.Grafana.Port)
	token, err := h.getToken()
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}
	_, err = client.R().
		SetHeaders(headers).
		SetResult(&gds).
		Get(url)
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	for _, ds := range gds {
		if clusterName == ds.Name {
			common.NewDefaultResponse().Succ(response, ds)
			return
		}
	}

	common.NewDefaultResponse().Succ(response, nil)
}

func (h *GrafanaHandler) HandleDatasourceCreate(req *restful.Request, response *restful.Response) {
	if config.SC.Grafana.Addr == "" || config.SC.Grafana.Port == "" {
		common.NewDefaultResponse().Fail(response, fmt.Errorf("Grafana.Addr is nil || Grafana.Port is nil"))
		return
	}
	var prometheusURL string
	clusterName := req.PathParameter("cluster_name")
	isAdmin := h.isMasterCluster()

	if isAdmin {
		endpoint, err := h.GetChildClusterIP(clusterName)
		log.Infof("get child cluster prometheus IP:  %s ", endpoint)
		if err != nil {
			log.Error(err.Error())
			common.NewDefaultResponse().Fail(response, err)
			return
		}
		prometheusURL = "http:" + endpoint + "/prometheus"
		log.Infof("child cluster prometheus %s was added grafana datasource", prometheusURL)
	} else {
		prometheusURL = "http://" + config.SC.Prometheus.Addr + ":" + config.SC.Prometheus.Port
		log.Infof("local cluster prometheus %s was added grafana datasource", prometheusURL)
	}
	url := fmt.Sprintf("http://%s:%s/grafana/api/datasources", config.SC.Grafana.Addr, config.SC.Grafana.Port)

	token, err := h.getToken()
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}

	headers := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": "Bearer " + token,
	}
	body := GrafanaDataSource{
		Name:      clusterName,
		Type:      "prometheus",
		URL:       prometheusURL,
		Access:    "proxy",
		BasicAuth: false,
		UID:       clusterName,
	}
	resp, err := resty.New().R().
		SetHeaders(headers).
		SetBody(body).
		Post(url)
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	common.NewDefaultResponse().Succ(response, resp)
}

func (h *GrafanaHandler) HandleDatasourceDelete(req *restful.Request, response *restful.Response) {
	client := resty.New()
	clusterName := req.PathParameter("cluster_name")
	url := fmt.Sprintf("http://%s:%s/grafana/api/datasources/uid/%s", config.SC.Grafana.Addr, config.SC.Grafana.Port, clusterName)

	token, err := h.getToken()
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}

	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}
	resp, err := client.R().
		SetHeaders(headers).
		Delete(url)
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	common.NewDefaultResponse().Succ(response, resp)
}

func (h *GrafanaHandler) GetChildClusterIP(childClusterName string) (string, error) {
	gvr := schema.GroupVersionResource{Group: "cluster.karmada.io", Version: "v1alpha1", Resource: "clusters"}
	unstructuredObj, err := h.karmadaClient.Resource(gvr).Namespace("").Get(context.TODO(), childClusterName, metav1.GetOptions{})
	if err != nil {
		return "", err
	}
	spec, found, err := unstructured.NestedMap(unstructuredObj.Object, "metadata.data")
	if err != nil || !found {
		return "", err
	}
	apiEndpoint, found, err := unstructured.NestedString(spec, "apiEndpoint")
	if err != nil || !found {
		return "", err
	}
	apiEndpoint = strings.Split(apiEndpoint, ":")[1]
	return apiEndpoint, nil
}

func (h *GrafanaHandler) isMasterCluster() bool {
	return config.EC.ClusterK8S != nil
}

type ServiceAccountResp struct {
	ID         int64  `json:"id"`
	Name       string `json:"name"`
	Login      string `json:"login"`
	OrgID      int64  `json:"orgId"`
	IsDisabled bool   `json:"isDisabled"`
	Role       string `json:"role"`
	Tokens     int64  `json:"tokens"`
	AvatarURL  string `json:"avatarUrl"`
}

func (h *GrafanaHandler) createServiceAccount() (*ServiceAccountResp, error) {
	sar := &ServiceAccountResp{}
	url := fmt.Sprintf("http://%s:%s/grafana/api/serviceaccounts", config.SC.Grafana.Addr, config.SC.Grafana.Port)
	client := resty.New()
	resp, err := client.R().
		SetBasicAuth(config.SC.Grafana.DefaultuserUser, config.SC.Grafana.DefaultuserPassword).
		SetHeader("Content-Type", "application/json").
		SetBody(map[string]string{
			"name": randomStr(),
			"role": "Admin",
		}).
		SetResult(sar).
		Post(url)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("token generaate failed, status code: %d, resp content: %s", resp.StatusCode(), resp.String())
	}

	return sar, nil
}

type ServiceAccountTokenResp struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
	Key  string `json:"key"`
}

func (h *GrafanaHandler) getToken() (string, error) {

	satr := &ServiceAccountTokenResp{}
	sar, err := h.createServiceAccount()
	if err != nil {
		return "", err
	}
	url := fmt.Sprintf("http://%s:%s/grafana/api/serviceaccounts/%d/tokens", config.SC.Grafana.Addr, config.SC.Grafana.Port, sar.ID)
	client := resty.New()
	resp, err := client.R().
		SetBasicAuth(config.SC.Grafana.DefaultuserUser, config.SC.Grafana.DefaultuserPassword).
		SetHeader("Content-Type", "application/json").
		SetBody(map[string]string{
			"name": randomStr(),
		}).
		SetResult(satr).
		Post(url)
	if err != nil {
		return "", err
	}
	if resp.IsError() {
		return "", fmt.Errorf("token generaate failed, status code: %d, resp content: %s", resp.StatusCode(), resp.String())
	}

	return satr.Key, nil
}

func randomStr() string {
	rand.Seed(uint64(time.Now().UnixNano()))
	var letters = []rune("abcdefghijklmnopqrstuvwxyz0123456789")
	b := make([]rune, 11)
	for i := range b {
		b[i] = letters[rand.Intn(len(letters))]
	}

	return string(b)
}
