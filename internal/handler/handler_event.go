package handler

import (
	"fmt"
	// event "hero-apiserver/internal/client/lokiclient/logging"
	"hero-apiserver/internal/common"
	"hero-apiserver/internal/config"
	event "hero-apiserver/internal/lokiclient"
	"hero-apiserver/internal/model"
	"hero-apiserver/internal/utils"
	"time"

	"github.com/emicklei/go-restful"
	log "github.com/sirupsen/logrus"
)

type EventHandler struct {
	client event.Logclient
}

func NewEventHandler() (*EventHandler, error) {
	loggingClient, err := event.NewLogClient(config.SC.GetLokiAddress(), common.NewHTTPClient())

	if err != nil {
		return nil, err
	}
	return &EventHandler{
		client: *loggingClient,
	}, nil
}

func (eh *EventHandler) QueryEvent(req *restful.Request, response *restful.Response) {
	query, err := eh.parseParameters(req)
	if err != nil {
		log.Errorln(err)
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	sf := eh.checkQueryParameter(query)

	result, err := eh.client.SearchEvents(sf)
	if err != nil {
		log.Error(err)
		common.NewDefaultResponse().Fail(response, err)
		return
	}

	common.NewDefaultResponse().Succ(response, result)

}

func (eh *EventHandler) parseParameters(req *restful.Request) (*model.EventQueryReq, error) {
	var reqparams model.EventQueryReq
	reqparams.NamespaceFilter = req.QueryParameter("namespaces")
	if len(reqparams.NamespaceFilter) == 0 {
		reqparams.NamespaceFilter = "hero-user"
	}
	reqparams.TaskID = req.QueryParameter("task_id")
	if len(reqparams.TaskID) == 0 {
		return nil, fmt.Errorf("params task_id err, not found")
	}
	var err error

	//开始时间往前推一小时
	reqparams.StartTime, err = timestamp2Time(req.QueryParameter("start_time"), time.Now().AddDate(0, 0, -10))
	if err != nil {
		return nil, err
	}
	reqparams.StartTime = reqparams.StartTime.Add(-1 * time.Hour)

	reqparams.EndTime, err = timestamp2Time(req.QueryParameter("end_time"), time.Now())
	if err != nil {
		return nil, err
	}
	return &reqparams, nil
}

func (eh *EventHandler) checkQueryParameter(query *model.EventQueryReq) event.EventSearchFilter {
	if query.StartTime.After(query.EndTime) {
		query.StartTime = query.EndTime.AddDate(0, 0, -10)
	}

	sf := event.EventSearchFilter{
		NamespaceFilter: utils.Split(query.NamespaceFilter, ","),
		EventFilter:     utils.Split(query.TaskID, ","),
		Sort:            "desc",
		Size:            100,
		StartTime:       query.StartTime,
		EndTime:         query.EndTime,
	}

	return sf
}
