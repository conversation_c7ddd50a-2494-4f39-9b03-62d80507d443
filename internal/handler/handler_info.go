//nolint:all
package handler

import (
	"hero-apiserver/internal/common"
	"hero-apiserver/internal/config"

	"github.com/emicklei/go-restful"
)

type ProductID struct {
}

func NewProductId() (*ProductID, error) {
	return &ProductID{}, nil
}

type result struct {
	UUID string `json:"uuid"`
}

func (pi *ProductID) QueryId(req *restful.Request, response *restful.Response) {
	r := &result{
		UUID: config.EC.ProductID,
	}

	common.NewDefaultResponse().Succ(response, r)
}
