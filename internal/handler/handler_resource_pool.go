package handler

import (
	"context"
	"hero-apiserver/internal/common"
	"hero-apiserver/internal/model"
	"strings"

	"github.com/emicklei/go-restful"
	log "github.com/sirupsen/logrus"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
)

var (
	RSidxMap = map[string]int{
		"cpu":        0,
		"memory":     1,
		"nvidia.com": 2,
	}
)

type RSPListResp struct {
	Items []ResourceItem `json:"items"`
}
type ResourceItem struct {
	MetricsName string     `json:"metric_name"`
	NodeDatas   []NodeItem `json:"node_datas"`
	Total       int64      `json:"total"`
	Idle        int64      `json:"idle"`
	Value       float64    `json:"vlue"`
}

type NodeItem struct {
	Name        string  `json:"name"`
	Type        string  `json:"type,omitempty"`
	Total       int64   `json:"total"`
	Idle        int64   `json:"idle"`
	Utilization float64 `json:"utilization"`
}

type ResourcePoolHandler struct {
	dynamicClient dynamic.Interface
}

func NewResourcePoolHandler(Client dynamic.Interface) (*ResourcePoolHandler, error) {
	return &ResourcePoolHandler{dynamicClient: Client}, nil
}

var (
	group     = "system.hero.ai"
	version   = "v1alpha1"
	resources = "resourcepools"
	gvr       = schema.GroupVersionResource{Group: group, Version: version, Resource: resources}
)

func (h *ResourcePoolHandler) HandleResourcePoolAction(req *restful.Request, response *restful.Response) {
	resourcePoolName := req.PathParameter("resourcepool")

	getOptions := metav1.GetOptions{}

	resourcepool, err := h.dynamicClient.Resource(gvr).Get(context.TODO(), resourcePoolName, getOptions)
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}

	resp, err := h.dealReault(resourcepool)

	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	common.NewDefaultResponse().Succ(response, resp)
}

func (h *ResourcePoolHandler) dealReault(resourcepool *unstructured.Unstructured) (RSPListResp, error) {

	var resourcePool model.ResourcePool
	resp := RSPListResp{
		Items: []ResourceItem{},
	}
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(resourcepool.UnstructuredContent(), &resourcePool); err != nil {
		return resp, err
	}
	RSPNodes := resourcePool.Status.Nodes

	CPUResource := h.NewResourceItem("cpu_utilisation")
	MemResource := h.NewResourceItem("mem_utilisation")
	GPUResource := h.NewResourceItem("gpu_utilisation")
	ResourceMap := []ResourceItem{CPUResource, MemResource, GPUResource}

	// 遍历节点，累计capability和 allocate
	for _, nodename := range RSPNodes {
		cpudata := h.NewNodeItem(nodename)
		memdata := h.NewNodeItem(nodename)
		gpudata := h.NewNodeItem(nodename)
		dataMap := []NodeItem{cpudata, memdata, gpudata}
		if err := h.UpdateInfoByNode(resourcePool, nodename, ResourceMap, dataMap); err != nil {
			return resp, err
		}
	}
	for _, resourceitem := range ResourceMap {
		if resourceitem.Total == 0 {
			resourceitem.Value = 0
		} else {
			resourceitem.Value = float64(resourceitem.Total-resourceitem.Idle) / float64(resourceitem.Total)
		}
		resp.Items = append(resp.Items, resourceitem)
	}
	return resp, nil
}

func (h *ResourcePoolHandler) NewNodeItem(name string) NodeItem {
	return NodeItem{
		Name:        name,
		Total:       0,
		Idle:        0,
		Utilization: 0.0,
	}
}

func (h *ResourcePoolHandler) NewResourceItem(name string) ResourceItem {
	return ResourceItem{
		MetricsName: name,
		Total:       0,
		Idle:        0,
		Value:       0.0,
	}
}

func (h *ResourcePoolHandler) GetResourceIdx(ResourceName string) int {

	for name, idx := range RSidxMap {
		if strings.Contains(ResourceName, name) {
			return idx
		}
	}
	return -1
}

func (h *ResourcePoolHandler) UpdateInfoByNode(resourcePool model.ResourcePool, nodename string, ResourceMap []ResourceItem, dataMap []NodeItem) error {
	var (
		idx int
	)
	nodeinfo := resourcePool.Status.Capabilities[nodename]

	for rn, value := range nodeinfo {
		if idx = h.GetResourceIdx(rn.String()); idx == -1 {
			continue
		}
		if v, flag := value.AsInt64(); flag {
			ResourceMap[idx].Total = ResourceMap[idx].Total + v
			ResourceMap[idx].Idle = ResourceMap[idx].Idle + v

			dataMap[idx].Total = dataMap[idx].Total + v
			dataMap[idx].Idle = dataMap[idx].Idle + v

		}

	}
	allocnode, ok := resourcePool.Status.Allocated[nodename]
	if ok {
		for rn, value := range allocnode {
			if idx = h.GetResourceIdx(rn.String()); idx == -1 {
				continue
			}
			if value, flag := value.AsInt64(); flag {
				ResourceMap[idx].Idle = ResourceMap[idx].Idle - value
				dataMap[idx].Idle = dataMap[idx].Idle - value
			}
		}
	}

	for idx, dataitem := range dataMap {
		if dataitem.Total == 0 {
			continue
		}

		dataitem.Utilization = float64(dataitem.Total-dataitem.Idle) / float64(dataitem.Total)

		ResourceMap[idx].NodeDatas = append(ResourceMap[idx].NodeDatas, dataitem)
	}
	return nil
}
