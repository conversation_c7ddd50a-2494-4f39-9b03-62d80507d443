//nolint:all
package handler

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"strconv"
	"strings"
	"sync"

	"golang.org/x/crypto/ssh"
	"gopkg.in/yaml.v2"

	"hero-apiserver/internal/common"

	"github.com/emicklei/go-restful"
	"github.com/pkg/errors"
	log "github.com/sirupsen/logrus"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8stypes "k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/kubernetes"
)

const (
	NodeStatusReady = iota + 1
	NodeStatusUnReady
	NodeStatusIsolation
)

const (
	FieldCreationTimeStamp      = "creationTimestamp"
	DEFAULTPORT                 = "22"
	ResourceTypeNodeLabelPrefix = "resource-type"
	EDGENODELABEL               = "node-role.kubernetes.io/edge"
	GPUNODERole                 = "node-role.kubernetes.io/gpu"
	NPUNODERole                 = "node-role.kubernetes.io/npu"
	CPUNodeRole                 = "node-role.kubernetes.io/cpu"
	LabelLocalDiskNodeRole      = "node-role.kubernetes.io/local-disk"
	LocalDiskCacheLabelKey      = "leinao.ai/local-disk-size"
	RESOURCEPOOLLABEL           = "resourcepool.system.hero.ai"

	Isolation = "isolation"
	Recover   = "recover"
	Ready     = "ready"

	NODEISOLATION   = `{"spec":{"unschedulable":true}}`
	NODEUNISOLATION = `{"spec":{"unschedulable":null}}`
	CARDISOLATION   = "lock"
	CARDUNISOLATION = "recover"
)

var (
	GPUMatched = map[string]string{
		"cpu": "CPU",
		"gpu": "NVIDIA",
		"npu": "Ascend",
	}
)

type TaskType string

const (
	TrainingJob TaskType = "Training"
	NotebookJob TaskType = "Notebook"
	PipelineJob TaskType = "Workflow"
	AppJob      TaskType = "App"
)

type NodeTaskResp struct {
	Result map[string]NodeTask
}

type NodeTask struct {
	Tasks []TaskInfo `json:"tasks,omitempty"`
}

type TaskInfo struct {
	Name  string   `json:"name,omitempty"`
	Type  TaskType `json:"type,omitempty"`
	State string   `json:"state,omitempty"`
}

type NodeListResp struct {
	Items      []NodeResult `json:"items"`
	TotalItems int          `json:"totalItems"`
}

type NodeResult struct {
	Name              string            `json:"name,omitempty"`
	Ip                string            `json:"ip,omitempty"`
	Status            int               `json:"status,omitempty"`
	Note              string            `json:"note,omitempty"`
	CreateTime        string            `json:"createTime,omitempty"`
	Port              string            `json:"port,omitempty"`
	MachineType       string            `json:"machineType,omitempty"`
	Resources         map[string]string `json:"resources,omitempty"`
	ResourcePool      string            `json:"resourcePool,omitempty"`
	EdgeNode          int               `json:"edgeNode,omitempty"`
	CpuType           string            `json:"cpuType,omitempty"`
	Arch              string            `json:"arch,omitempty"`
	NetworkCards      []NetworkCard     `json:"networkCard,omitempty"`
	Disks             []Disk            `json:"disks,omitempty"`
	FileSystems       []FileSystem      `json:"fileSystems,omitempty"`
	ContainerRuntime  string            `json:"containerRuntime,omitempty"`
	OSVersion         string            `json:"operateSystemVersion,omitempty"`
	GpuType           string            `json:"gpuType,omitempty"`
	CacheDiskCapacity string            `json:"cacheDiskCapacity,omitempty"`
}

type FileSystem struct {
	Name      string `json:"name,omitempty"`
	Type      string `json:"type,omitempty"`
	Size      string `json:"size,omitempty"`
	Used      string `json:"used,omitempty"`
	Avail     string `json:"avail,omitempty"`
	Utils     string `json:"utils,omitempty"`
	MountPath string `json:"mountpath,omitempty"`
}

type Disk struct {
	Name     string `json:"name,omitempty"`
	Capacity string `json:"capacity,omitempty"`
}

type NetworkCard struct {
	Type string `json:"type,omitempty"`
}

type CardActionReq struct {
	CardName string `json:"cardName,omitempty"`
	CardID   string `json:"cardID"`
	NodeName string `json:"nodeName"`
	CardType string `json:"cardType,omitempty"`
	Note     string `json:"note,omitempty"`
}

type CardStatusInfo struct {
	Uuid   string `yaml:"uuid"`
	Action string `yaml:"action"`
}

type CardConfigMap struct {
	Info []CardStatusInfo `yaml:"info"`
}

type nodeHandler struct {
	k8sclient kubernetes.Interface
	ssconfig  *ssh.ClientConfig
}

func NewNodeHandler(k8sClient kubernetes.Interface, ssconfig *ssh.ClientConfig) (*nodeHandler, error) {
	return &nodeHandler{k8sclient: k8sClient, ssconfig: ssconfig}, nil
}

func (h *nodeHandler) HandleListNodeTask(req *restful.Request, response *restful.Response) {
	podList, err := h.k8sclient.CoreV1().Pods("hero-user").List(
		context.Background(),
		metav1.ListOptions{},
	)
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}

	//query nodes
	nodeId := req.QueryParameter("nodeId")
	reqNodes := strings.Split(nodeId, ",")
	nodeMap := map[string]struct{}{}
	for _, node := range reqNodes {
		nodeMap[node] = struct{}{}
	}

	var result = make(map[string]NodeTask)
OuterLoop:
	for _, pod := range podList.Items {
		switch pod.Status.Phase {
		case v1.PodFailed, v1.PodSucceeded, v1.PodUnknown:
			continue
		}

		if len(pod.Spec.NodeName) == 0 {
			continue
		}

		if len(nodeId) != 0 {
			if _, found := nodeMap[pod.Spec.NodeName]; !found {
				continue
			}
		}

		for _, containerStatus := range pod.Status.ContainerStatuses {
			if containerStatus.State.Waiting != nil {
				if containerStatus.State.Waiting.Reason == "ImagePullBackOff" || containerStatus.State.Waiting.Reason == "ErrImagePull" {
					continue OuterLoop
				}
			}
		}

		//忽略掉Terminating状态
		if !pod.DeletionTimestamp.IsZero() {
			continue
		}

		//pipeline trainingjob notebook app
		taskInfo := TaskInfo{}
		var taskType TaskType
		var taskname string
		labels := pod.GetLabels()
		if _, found := labels["pipeline/runid"]; found {
			taskType = PipelineJob
			taskname = labels["pipeline/runid"]
		} else if _, found := labels["notebooks.system.hero.ai/name"]; found {
			taskType = NotebookJob
			taskname = labels["notebooks.system.hero.ai/name"]
		} else if _, found := labels["system.hero.ai/job-name"]; found {
			taskType = TrainingJob
			taskname = labels["system.hero.ai/job-name"]
		} else if _, found := labels["app"]; found {
			taskType = AppJob
			taskname = labels["leinao.ai/app-name"]
		}

		if len(taskType) == 0 {
			continue
		}

		tasks := result[pod.Spec.NodeName].Tasks
		taskInfo.Type = taskType
		taskInfo.Name = taskname
		taskInfo.State = string(pod.Status.Phase)

		if _, found := result[pod.Spec.NodeName]; !found {
			tasks = []TaskInfo{taskInfo}
		} else {
			tasks = append(tasks, taskInfo)
		}
		result[pod.Spec.NodeName] = NodeTask{
			Tasks: tasks,
		}
	}

	for index, r := range result {
		result[index] = h.removeDuplicates(r.Tasks)
	}
	common.NewDefaultResponse().Succ(response, result)
}

func (h *nodeHandler) removeDuplicates(slice []TaskInfo) NodeTask {
	seen := make(map[string]struct{})
	result := []TaskInfo{}

	for _, value := range slice {
		if _, found := seen[value.Name]; !found {
			seen[value.Name] = struct{}{}
			result = append(result, value)
		}
	}

	return NodeTask{Tasks: result}
}

func (h *nodeHandler) HandleListNode(req *restful.Request, response *restful.Response) {
	var nodeListResp NodeListResp
	var wg sync.WaitGroup
	var mtx sync.Mutex
	nodeName := req.QueryParameter("node_name")
	nodeList, err := h.k8sclient.CoreV1().Nodes().List(
		context.Background(),
		metav1.ListOptions{},
	)
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	if nodeName != "" {
		for _, node := range nodeList.Items {

			if node.Name != nodeName {
				continue
			}
			nodeData := h.nodeResultTask(node)

			nodeListResp.Items = append(nodeListResp.Items, nodeData)

		}
	} else {
		for _, node := range nodeList.Items {
			wg.Add(1)
			go func(node v1.Node) {
				defer wg.Done()
				nodeData := h.nodeResultTask(node)
				mtx.Lock()
				nodeListResp.Items = append(nodeListResp.Items, nodeData)
				mtx.Unlock()
			}(node)

		}
		wg.Wait()
	}

	nodeListResp.TotalItems = len(nodeListResp.Items)
	common.NewDefaultResponse().Succ(response, nodeListResp)
}

func (h *nodeHandler) getCpuInfo(nodeName string) (any, error) {
	command := "lscpu | grep 'Model name:' | head -n 1"
	res, err := h.RunSHHCommand(nodeName, command)
	if err != nil {
		return "", err
	}
	return strings.TrimSpace(strings.Split(res, ":")[1]), nil
}

func (h *nodeHandler) getNetworkInfo(nodeName string) (any, error) {
	networkCards := make([]NetworkCard, 0)
	command := "lspci | grep -E 'Ethernet | Infiniband'"
	res, err := h.RunSHHCommand(nodeName, command)
	if err != nil {
		return networkCards, err
	}
	lines := strings.Split(res, "\n")
	for i := 0; i < len(lines); i++ {
		if lines[i] == "" {
			continue
		}
		networkCards = append(networkCards, NetworkCard{Type: strings.TrimSpace(strings.Split(lines[i], "controller:")[1])})
	}
	return networkCards, nil
}

func (h *nodeHandler) getFileSysytemInfo(nodeName string) (any, error) {
	command := "df -Th"
	fs := make([]FileSystem, 0)

	ignorefs := map[string]interface{}{
		"overlay":  nil,
		"shm":      nil,
		"tmpfs":    nil,
		"devtmpfs": nil,
	}
	res, err := h.RunSHHCommand(nodeName, command)
	if err != nil {
		return fs, err
	}
	lines := strings.Split(res, "\n")
	for i := 1; i < len(lines); i++ {
		tmp := make([]string, 0)
		info := strings.Split(lines[i], " ")
		for _, v := range info {
			if v != "" {
				tmp = append(tmp, v)
			}
		}
		if len(info) == 1 {
			break
		}
		if _, ok := ignorefs[tmp[1]]; ok {
			continue
		}
		if _, ok := ignorefs[tmp[0]]; ok {
			continue
		}

		fs = append(fs, FileSystem{
			Name:      tmp[0],
			Type:      tmp[1],
			Size:      tmp[2],
			Used:      tmp[3],
			Avail:     tmp[4],
			Utils:     tmp[5],
			MountPath: tmp[6]})
	}
	return fs, nil
}

func (h *nodeHandler) getDiskInfo(nodeName string) (any, error) {
	disks := make([]Disk, 0)
	command := "fdisk -l | grep 'Disk /dev/'"
	res, err := h.RunSHHCommand(nodeName, command)
	if err != nil {
		return disks, err
	}
	lines := strings.Split(res, "\n")
	for _, line := range lines {
		info := strings.Split(line, ":")
		if len(info) == 1 {
			break
		}
		if strings.Contains(info[0], "/mapper") {
			continue
		}
		name := strings.Split(strings.TrimSpace(info[0]), " ")[1]
		capcity := strings.Split(strings.TrimSpace(info[1]), ",")[0]
		disks = append(disks, Disk{Name: name, Capacity: capcity})
	}
	return disks, nil
}

func (h *nodeHandler) RunSHHCommand(nodeName, command string) (string, error) {

	client, err := ssh.Dial("tcp", nodeName+":22", h.ssconfig)
	if err != nil {
		return "", err
	}
	defer client.Close()

	session, err := client.NewSession()
	if err != nil {
		return "", err
	}
	defer session.Close()
	var b bytes.Buffer
	session.Stdout = &b
	err = session.Run(command)
	if err != nil {
		return "", err
	}
	return b.String(), nil

}

func (h *nodeHandler) nodeResultTask(node v1.Node) NodeResult {
	var wg sync.WaitGroup
	var mtx sync.Mutex
	labels := node.GetLabels()
	machineType := selectMachineType(node.GetLabels())
	nodeIP := node.Status.Addresses[0].Address
	nodeData := NodeResult{
		Name:             node.GetName(),
		Ip:               nodeIP,
		CreateTime:       node.CreationTimestamp.String(),
		Status:           NodeStatusReady,
		Port:             DEFAULTPORT,
		MachineType:      machineType,
		GpuType:          GPUMatched[machineType],
		ResourcePool:     getResourcePoolName(labels),
		Resources:        getResourceList(node, machineType),
		OSVersion:        node.Status.NodeInfo.OSImage,
		ContainerRuntime: node.Status.NodeInfo.ContainerRuntimeVersion,
		Arch:             node.Status.NodeInfo.Architecture,
	}

	fetchInfo := func(fetchFunc func(string) (any, error), nodeIP string, nodeData *NodeResult, setFunc func(*NodeResult, any)) {
		wg.Add(1)
		go func() {
			defer wg.Done()
			info, err := fetchFunc(nodeIP)
			if err != nil {
				log.Error(err.Error())
				return

			}
			mtx.Lock()
			setFunc(nodeData, info)
			mtx.Unlock()
		}()
	}

	fetchInfo(h.getCpuInfo, nodeIP, &nodeData, func(nodeData *NodeResult, cputype any) {
		nodeData.CpuType = cputype.(string)
	})

	fetchInfo(h.getDiskInfo, nodeIP, &nodeData, func(nodeData *NodeResult, disks any) {
		nodeData.Disks = disks.([]Disk)
	})

	fetchInfo(h.getNetworkInfo, nodeIP, &nodeData, func(nodeData *NodeResult, networkCards any) {
		nodeData.NetworkCards = networkCards.([]NetworkCard)
	})

	fetchInfo(h.getFileSysytemInfo, nodeIP, &nodeData, func(nodeData *NodeResult, fileSystems any) {
		nodeData.FileSystems = fileSystems.([]FileSystem)
	})

	wg.Wait()

	status := strings.ToUpper(string(node.Status.Conditions[len(node.Status.Conditions)-1].Type))
	if status == strings.ToUpper(Ready) && node.Status.Conditions[len(node.Status.Conditions)-1].Status == v1.ConditionTrue && !node.Spec.Unschedulable {
		nodeData.Status = NodeStatusReady
	} else if node.Spec.Unschedulable {
		nodeData.Status = NodeStatusIsolation
	}

	nodeLabels := node.GetLabels()

	if _, ok := nodeLabels[CPUNodeRole]; !ok {
		if !IsComputeResorceExisted(node) {
			nodeData.Status = NodeStatusUnReady
		}
	}

	if value, ok := nodeLabels[LabelLocalDiskNodeRole]; ok {
		if value == "true" {
			nodeData.CacheDiskCapacity = nodeLabels[LocalDiskCacheLabelKey]
		}
	}
	return nodeData
}

func IsComputeResorceExisted(node v1.Node) bool {
	resources := map[string]string{}
	for rn, value := range node.Status.Capacity {
		if (strings.Contains(rn.String(), "nvidia.com") || strings.Contains(rn.String(), "huawei.com")) && value.AsDec().String() != "0" {
			gpuKey := rn.String()
			resources[gpuKey] = value.AsDec().String()
			return true
		}

	}
	return false
}

func (h *nodeHandler) HandleListNodeForPlatform(req *restful.Request, response *restful.Response) {
	nodeName := req.QueryParameter("node_name")
	labelSelector := req.QueryParameter("labelSelector")
	limit := req.QueryParameter("limit")
	page := req.QueryParameter("page")

	nodeList, err := h.k8sclient.CoreV1().Nodes().List(
		context.Background(),
		metav1.ListOptions{
			LabelSelector: labelSelector,
		},
	)
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}

	var nodeListResp NodeListResp
	var nodeListScreen []v1.Node
	if nodeName != "" {
		for _, node := range nodeList.Items {
			if node.GetName() != nodeName {
				continue
			}
			nodeListScreen = append(nodeListScreen, node)
		}
	} else {
		if limit != "" && page != "" {
			totalNumber := len(nodeList.Items)
			var totalPage int
			limitint, _ := strconv.Atoi(limit)
			pageint, _ := strconv.Atoi(page)
			divisor := len(nodeList.Items) / limitint
			remainder := len(nodeList.Items) % limitint
			if remainder == 0 {
				totalPage = divisor
			} else {
				totalPage = divisor + 1
			}
			if pageint > totalPage {
				common.NewDefaultResponse().Fail(response, errors.New(fmt.Sprintf("NodeList requests params page: %v greater than totalPage: %v", page, totalPage)))
				return
			}

			startIndex := limitint * (pageint - 1)
			endIndex := startIndex + limitint
			if endIndex > totalNumber {
				endIndex = totalNumber
			}
			nodeListScreen = append(nodeListScreen, nodeList.Items[startIndex:endIndex]...)
		} else {
			nodeListScreen = append(nodeListScreen, nodeList.Items...)
		}
	}

	for _, node := range nodeListScreen {
		nodeData := nodeResultHandle(node)
		nodeListResp.Items = append(nodeListResp.Items, nodeData)
	}
	nodeListResp.TotalItems = len(nodeListScreen)

	common.NewDefaultResponse().Succ(response, nodeListResp)
}

func nodeResultHandle(node v1.Node) NodeResult {

	machineType := selectMachineType(node.GetLabels())
	nodeData := &NodeResult{
		Name:         node.GetName(),
		Ip:           node.Status.Addresses[0].Address,
		CreateTime:   node.CreationTimestamp.String(),
		Status:       NodeStatusReady,
		Port:         DEFAULTPORT,
		MachineType:  machineType,
		ResourcePool: getResourcePoolName(node.GetLabels()),
		Resources:    getResourceList(node, machineType),
	}
	status := strings.ToUpper(string(node.Status.Conditions[len(node.Status.Conditions)-1].Type))
	if status == strings.ToUpper(Ready) && node.Status.Conditions[len(node.Status.Conditions)-1].Status == v1.ConditionTrue && !node.Spec.Unschedulable {
		nodeData.Status = NodeStatusReady
	} else if node.Spec.Unschedulable {
		nodeData.Status = NodeStatusIsolation
	}

	return *nodeData
}

// selectMachineType 返回 node 上的加速计算卡(GPU,NPU)名称，比如 gtx-1080-ti, Ascend910, 不存在计算卡则返回空字符串
func selectMachineType(labels map[string]string) string {
	for key, value := range labels {
		if GPUNODERole == key && value == "true" {
			return "gpu"
		}
		if NPUNODERole == key && value == "true" {
			return "npu"
		}
	}
	return "cpu"
}

func getResourceList(node v1.Node, machineType string) map[string]string {

	capacity := node.Status.Capacity

	resourcelist := map[string]string{}

	for rn, value := range capacity {

		if rn.String() == "cpu" || rn.String() == "memory" {
			resourcelist[rn.String()] = value.AsDec().String()
		}

		if strings.Contains(rn.String(), "rdma") && value.AsDec().String() != "0" {
			rdmaKey := rn.String()
			resourcelist[rdmaKey] = value.AsDec().String()
		}

		if machineType == "gpu" {
			if strings.Contains(rn.String(), "nvidia.com") && value.AsDec().String() != "0" {
				gpuKey := rn.String()
				resourcelist[gpuKey] = value.AsDec().String()

			}

		}
		if machineType == "npu" {
			if strings.Contains(rn.String(), "huawei.com") && value.AsDec().String() != "0" {
				gpuKey := rn.String()
				resourcelist[gpuKey] = value.AsDec().String()

			}

		}

	}

	return resourcelist

}

func getResourcePoolName(labels map[string]string) string {
	for key, value := range labels {

		if RESOURCEPOOLLABEL == key {
			return value
		}
	}
	return ""
}

func (h *nodeHandler) HandleNodeAction(req *restful.Request, response *restful.Response) {
	nodeName := req.PathParameter("name")
	action := req.PathParameter("action")

	_, err := h.k8sclient.CoreV1().Nodes().Get(
		context.Background(),
		nodeName,
		metav1.GetOptions{},
	)
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}

	patchData := ""
	switch action {
	case Isolation:
		patchData = NODEISOLATION
	case Recover:
		patchData = NODEUNISOLATION
	default:
		common.NewDefaultResponse().Fail(response, errors.New(fmt.Sprintf("NodeAction, unknown node action option, operation must be: %s", "isolation or recover")))
		return
	}

	_, err = h.k8sclient.CoreV1().Nodes().Patch(
		context.Background(),
		nodeName,
		k8stypes.MergePatchType,
		[]byte(patchData),
		metav1.PatchOptions{},
	)
	if err != nil {
		common.NewDefaultResponse().Fail(response, err)
		return
	}

	common.NewDefaultResponse().Succ(response, nil)
}

func (h *nodeHandler) HandleCardAction(req *restful.Request, response *restful.Response) {
	card := req.PathParameter("card")
	action := req.PathParameter("action")

	var cardActionReq = &CardActionReq{}
	inBody, err := io.ReadAll(req.Request.Body)
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	if err := json.Unmarshal(inBody, cardActionReq); err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	if card != cardActionReq.CardID {
		common.NewDefaultResponse().Fail(response, errors.New("param invalid"))
		return
	}
	// configmap 对象构建
	configMapInterface := h.k8sclient.CoreV1().ConfigMaps("nvidia-device-plugin")
	configMap, err := configMapInterface.Get(context.Background(), "isolation-config", metav1.GetOptions{})
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}

	var cardConfig CardConfigMap
	if err := yaml.Unmarshal([]byte(configMap.Data["config.yaml"]), &cardConfig); err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	cardConf, err := cardConfigHandle(&cardConfig, cardActionReq, action)
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	cardConfigMarshal, err := yaml.Marshal(cardConf)
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	configMap.Data["config.yaml"] = string(cardConfigMarshal)
	if _, err := configMapInterface.Update(context.Background(), configMap, metav1.UpdateOptions{}); err != nil {
		log.Errorf("update GpuCard Status Failed %s , %v", cardActionReq.CardID, err)
		common.NewDefaultResponse().Fail(response, err)
		return
	}

	common.NewDefaultResponse().Succ(response, nil)
}

func cardConfigHandle(cardConfig *CardConfigMap, cardActionReq *CardActionReq, action string) (*CardConfigMap, error) {
	numi := -1
	for i := 0; i < len(cardConfig.Info); i++ {
		if cardConfig.Info[i].Uuid == cardActionReq.CardID {
			numi = i
			break
		}
	}
	switch action {
	case Isolation:
		if cardActionReq.CardID == "" || cardActionReq.NodeName == "" {
			log.Errorf("isolation card require params cardID and nodeName cardID:%s,nodeName:%s", cardActionReq.CardID, cardActionReq.NodeName)
			//common.NewDefaultResponse().Fail(response, errors.New("isolation card require params cardID and nodeName"))
			return nil, errors.New("isolation card require params cardID and nodeName")
		}
		if numi != -1 {
			cardConfig.Info[numi].Action = CARDISOLATION
		} else {
			cardConfig.Info = append(cardConfig.Info, CardStatusInfo{Uuid: cardActionReq.CardID, Action: CARDISOLATION})
		}
	case Recover:
		if numi != -1 {
			cardConfig.Info[numi].Action = CARDUNISOLATION
		}
	default:
		log.Errorf("unknown card action,option isolation or recover cardID:%s,action:%s", cardActionReq.CardID, action)
		//common.NewDefaultResponse().Fail(response, errors.New("unknown card action,option isolation or recover"))
		return nil, errors.New("unknown card action,option isolation or recover")
	}

	return cardConfig, nil
}

func GetGPUNumForNode(node v1.Node, gpuType string) (int64, error) {

	allocatable := node.Status.Allocatable

	var count int64
	for rn, value := range allocatable {

		if value, flag := value.AsInt64(); rn.String() == gpuType && flag && value != 0 {

			return value, nil
		} else if strings.Contains(rn.String(), gpuType) {
			count += value
		}
	}
	if count != 0 {
		return count, nil
	}
	return -1, fmt.Errorf("not exist gpuo type: '%s'", gpuType)
}
