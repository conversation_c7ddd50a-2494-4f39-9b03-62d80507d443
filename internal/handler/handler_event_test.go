package handler

import (
	"hero-apiserver/internal/model"
	"net/http"
	"testing"
	"time"

	"github.com/emicklei/go-restful"
	"github.com/stretchr/testify/assert"
)

func TestParseParameters(t *testing.T) {
	// Create a new http.Request with query parameters
	reqURL := "/events?task_id=task-123&start_time=1609459200000000000&end_time=1609462800000000000"
	httpReq, err := http.NewRequest("GET", reqURL, nil)
	if err != nil {
		t.Fatalf("Error creating request: %v", err)
	}

	// Create a restful.Request from http.Request
	req := restful.NewRequest(httpReq)

	// Create the handler
	handler := &EventHandler{}

	// Call the parseParameters function
	params, err := handler.parseParameters(req)

	// Assertions
	assert.Nil(t, err)
	assert.Equal(t, "task-123", params.TaskID)
	assert.Equal(t, "hero-user", params.NamespaceFilter)
	assert.True(t, params.StartTime.Before(params.EndTime)) // Ensure StartTime is valid and before EndTime
}

func TestCheckQueryParameter(t *testing.T) {
	handler := &EventHandler{}

	// Sample input parameters
	query := &model.EventQueryReq{
		NamespaceFilter: "hero-user",
		TaskID:          "task-123",
		StartTime:       time.Now().Add(-1 * time.Hour),
		EndTime:         time.Now(),
	}

	sf := handler.checkQueryParameter(query)

	// Assertions
	assert.Equal(t, []string{"hero-user"}, sf.NamespaceFilter)
	assert.Equal(t, []string{"task-123"}, sf.EventFilter)
	assert.Equal(t, "desc", sf.Sort)
	assert.Equal(t, int64(100), sf.Size)
	assert.Equal(t, query.StartTime, sf.StartTime)
	assert.Equal(t, query.EndTime, sf.EndTime)
}
