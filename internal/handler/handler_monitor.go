package handler

import (
	"hero-apiserver/internal/common"
	"hero-apiserver/internal/config"
	"hero-apiserver/internal/model"
	"hero-apiserver/internal/monitor"
	"strconv"
	"time"

	log "github.com/sirupsen/logrus"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"

	"github.com/emicklei/go-restful"
)

type MonitorHandler struct {
	ps monitor.PrometheusServer
}

func NewMonitorHandler(dynamicClient dynamic.Interface, clientSet kubernetes.Interface) (*MonitorHandler, error) {
	mOptions := monitor.NewPrometheusOptions(config.SC.GetPrometheusAddress())
	prometheusClient, err := monitor.NewPrometheusClient(mOptions)
	prometheusServer := monitor.NewPrometheusServer(prometheusClient, dynamicClient, clientSet)
	return &MonitorHandler{ps: prometheusServer}, err
}

func (h *MonitorHandler) HandleGPUListQuery(req *restful.Request, response *restful.Response) {
	q, err := h.MakeQueryOptions(req)
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	res, err := h.ps.GPUCardList(q)
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	common.NewDefaultResponse().Succ(response, res)
}

func (h *MonitorHandler) HandleGPULockQuery(req *restful.Request, response *restful.Response) {
	q, err := h.MakeQueryOptions(req)
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	err = h.ps.GPUCardLock(q)
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	common.NewDefaultResponse().Succ(response, "GPUcard Lock Successed")
}

func (h *MonitorHandler) HandleGPURecoverQuery(req *restful.Request, response *restful.Response) {
	q, err := h.MakeQueryOptions(req)
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	err = h.ps.GPUCardRecover(q)
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	common.NewDefaultResponse().Succ(response, "GPUcard Recover Successed")
}

func (h *MonitorHandler) HandleResourcePoolQuery(req *restful.Request, response *restful.Response) {

	q, err := h.MakeQueryOptions(req)
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	var res []*monitor.Metric

	if q.Time.IsZero() {
		res = h.ps.ResourcePoolQueryRangeTime(q)
	} else {
		res = h.ps.ResourcePoolQueryCurrentTime(q)
	}
	common.NewDefaultResponse().Succ(response, res)
}

func (h *MonitorHandler) HandleClusterComponentQuery(req *restful.Request, response *restful.Response) {
	res, err := h.ps.ClusterPodInfo()
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	common.NewDefaultResponse().Succ(response, res)
}

func (h *MonitorHandler) HandleClusterQuery(req *restful.Request, response *restful.Response) {
	q, err := h.MakeQueryOptions(req)
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	var res []*monitor.Metric

	if q.Time.IsZero() {
		res = h.ps.ClusterQueryRangeTime(q)
	} else {
		res = h.ps.ClusterQueryCurrentTime(q)
	}
	common.NewDefaultResponse().Succ(response, res)
}

func (h *MonitorHandler) HandleNodeQuery(req *restful.Request, response *restful.Response) {
	q, err := h.MakeQueryOptions(req)
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	var res []*monitor.Metric

	if q.Time.IsZero() {
		res = h.ps.NodeQueryRangeTime(q)
	} else {
		res = h.ps.NodeQueryCurrentTime(q)
	}

	common.NewDefaultResponse().Succ(response, res)
}

func (h *MonitorHandler) HandleJobQuery(req *restful.Request, response *restful.Response) {
	q, err := h.MakeQueryOptions(req)
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	var res []*monitor.Metric
	if q.MetricsName == "" {
		log.Error("query monitor metrics fialed: metricsName is nil")
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	res = h.ps.JobQueryRangeTime(q)
	res = monitor.ComputeRate(res)
	if q.AloneFromRangeTime {
		res = monitor.AloneValueByRangeTime(res)
	}
	common.NewDefaultResponse().Succ(response, res)
}

type RPReq struct {
	ResourePoolName string `json:"resource_pool_name"`
}

func (h *MonitorHandler) MakeQueryOptions(req *restful.Request) (q model.QueryOptions, err error) {
	rpnames := RPReq{} // json方式读取请求体数据
	req.ReadEntity(&rpnames)
	q.ResourcePoolName = rpnames.ResourePoolName

	q.NodeName = req.PathParameter("node_name")
	q.UUID = req.PathParameter("uuid")
	q.Namespace = req.QueryParameter("namespace")
	q.ReponseCode = req.QueryParameter("response_code")
	q.MetricsName = req.QueryParameter("metrics_name")
	q.ModeName = req.QueryParameter("mode_name")
	q.StatisticalTime = req.QueryParameter("statistical_time")

	currentTime := req.QueryParameter("time")
	startTime := req.QueryParameter("start")
	endTime := req.QueryParameter("end")
	stepDurition := req.QueryParameter("step")
	taskType := req.QueryParameter("task_type")

	if taskType != "" {
		q.JobName = taskType + "-" + req.PathParameter("job_name")
	} else {
		q.JobName = req.PathParameter("job_name")
	}

	if currentTime != "" {
		q.Time, _ = h.parseTime(currentTime)
	} else {
		if startTime != "" && endTime != "" {
			q.Start, _ = h.parseTime(startTime)
			q.End, _ = h.parseTime(endTime)
		}
		if stepDurition == "" {
			q.AloneFromRangeTime = true
			q.Step = model.DefaultStep
		} else {
			q.Step, _ = time.ParseDuration(stepDurition)
		}
	}

	return q, nil
}

func (h *MonitorHandler) parseTime(timeStr string) (time.Time, error) {
	timeInt, err := strconv.ParseInt(timeStr, 10, 64)
	return time.Unix(timeInt, 0), err
}
