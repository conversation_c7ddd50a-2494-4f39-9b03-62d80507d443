//nolint:all
package handler

import (
	"context"
	"fmt"
	"hero-apiserver/internal/common"
	"hero-apiserver/internal/config"
	"io"
	"strings"
	"time"

	"github.com/emicklei/go-restful"
	log "github.com/sirupsen/logrus"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8stypes "k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/json"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/record"
	batch "volcano.sh/apis/pkg/apis/batch/v1alpha1"
	"volcano.sh/apis/pkg/apis/helpers"
	schedulingv1 "volcano.sh/apis/pkg/apis/scheduling/v1beta1"
	"volcano.sh/apis/pkg/client/clientset/versioned"
	"volcano.sh/volcano/cmd/scheduler/app/options"
	jobhelpers "volcano.sh/volcano/pkg/controllers/job/helpers"
	_ "volcano.sh/volcano/pkg/scheduler/actions"
	"volcano.sh/volcano/pkg/scheduler/api"
	"volcano.sh/volcano/pkg/scheduler/cache"
	"volcano.sh/volcano/pkg/scheduler/conf"
	"volcano.sh/volcano/pkg/scheduler/framework"
	"volcano.sh/volcano/pkg/scheduler/plugins"
	volschutil "volcano.sh/volcano/pkg/scheduler/util"
)

type taskHandler struct {
	k8sclient     kubernetes.Interface
	volcanoclient *versioned.Clientset
	schedulerConf *conf.SchedulerConfiguration
}

func NewTaskHandler(k8sClient kubernetes.Interface, volcanoClient *versioned.Clientset, schedulerConf *conf.SchedulerConfiguration) (*taskHandler, error) {
	return &taskHandler{k8sclient: k8sClient, volcanoclient: volcanoClient, schedulerConf: schedulerConf}, nil
}

type FakeScheduler struct {
	SchedulerConf  string
	Actions        []framework.Action
	Plugins        []conf.Tier
	Configurations []conf.Configuration
}

type FreeResourceReq struct {
	Kind         string      `json:"kind"`
	JobName      string      `json:"jobName"`
	ResourcePool string      `json:"resourcePool"`
	TaskRoles    []*TaskRole `json:"taskRoles"`
	ApiVersion   string      `json:"apiVersion"`
}

type TaskRole struct {
	ShmMB        int64      `json:"shmMB,omitempty"`
	MemoryMB     int64      `json:"memoryMB"`
	TaskRoleName string     `json:"taskRoleName"`
	CpuNumber    int64      `json:"cpuNumber"`
	CpuArch      string     `json:"cpuArch,omitempty"`
	TaskNumber   int64      `json:"taskNumber"`
	AccDevice    *AccDevice `json:"accDevice,omitempty"`
}

type AccDevice struct {
	Kind       string `json:"kind"`
	Requests   int64  `json:"requests"`
	DeviceType string `json:"deviceType"`
}

type FreeResourceResp struct {
	Allocatable int `json:"allocatable"` // 0-没有空闲资源分配给任务；1-有空闲资源分配给任务
}

func (h *taskHandler) HandleQueryFreeResource(req *restful.Request, response *restful.Response) {
	var freeResourceReq = &FreeResourceReq{}
	inBody, err := io.ReadAll(req.Request.Body)
	if err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	if err := json.Unmarshal(inBody, freeResourceReq); err != nil {
		log.Error(err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}

	// 查询 kubernetes 集群所有 node
	nodeList, err := h.k8sclient.CoreV1().Nodes().List(
		context.Background(),
		metav1.ListOptions{},
	)
	if err != nil {
		log.Errorf("HandleQueryFreeResource k8s node list error: %s", err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	int32convert := func(v int32) *int32 {
		return &v
	}
	vcJob := &batch.Job{}
	vcJob.Kind = freeResourceReq.Kind
	vcJob.Name = freeResourceReq.JobName
	vcJob.Spec.Queue = freeResourceReq.ResourcePool
	taskNumSum := int64(0)
	for _, task := range freeResourceReq.TaskRoles {
		taskSpec := &batch.TaskSpec{}
		taskSpec.Name = task.TaskRoleName
		taskSpec.Replicas = int32(task.TaskNumber)
		taskSpec.MinAvailable = int32convert(int32(task.TaskNumber))
		taskNumSum += task.TaskNumber
		if task.CpuArch != "" {
			nodeselector := make(map[string]string)
			nodeselector["kubernetes.io/arch"] = task.CpuArch
			taskSpec.Template.Spec.NodeSelector = nodeselector
		}
		containerSpec := v1.Container{}
		containerSpec.Name = task.TaskRoleName
		containerSpec.Resources.Limits = getResourceLimit(task, nodeList)
		containerSpec.Resources.Requests = getResourceLimit(task, nodeList)
		taskSpec.Template.Spec.Containers = append(taskSpec.Template.Spec.Containers, containerSpec)
		vcJob.Spec.Tasks = append(vcJob.Spec.Tasks, *taskSpec)
	}
	vcJob.Spec.MinAvailable = int32(taskNumSum)
	vcJob.APIVersion = freeResourceReq.ApiVersion

	queue, err := h.volcanoclient.SchedulingV1beta1().Queues().Get(context.TODO(), freeResourceReq.ResourcePool, metav1.GetOptions{})
	if err != nil {
		log.Errorf("HandleQueryFreeResource volcano queues error: %s", err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}

	podList, err := h.k8sclient.CoreV1().Pods("").List(
		context.Background(),
		metav1.ListOptions{},
	)
	if err != nil {
		log.Errorf("HandleQueryFreeResource k8s pod list error: %s", err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	var pods []v1.Pod
	for _, p := range podList.Items {
		if len(p.Spec.NodeName) != 0 && p.Spec.SchedulerName == "volcano" {
			if val, found := p.Labels["resourcepool.system.hero.ai"]; found && val == freeResourceReq.ResourcePool {
				pods = append(pods, p)
			}
		}
	}

	var freeResourceResp FreeResourceResp
	ok, err := h.isJobAllocatable(vcJob, nodeList.Items, queue, pods)
	if err != nil {
		log.Errorf("HandleQueryFreeResource volcano isJobAllocatable error: %s", err.Error())
		common.NewDefaultResponse().Fail(response, err)
		return
	}
	if ok {
		freeResourceResp.Allocatable = 1
	} else {
		freeResourceResp.Allocatable = 0
	}

	common.NewDefaultResponse().Succ(response, freeResourceResp)
}

func getResourceLimit(task *TaskRole, nodes *v1.NodeList) v1.ResourceList {
	resourceLimits := v1.ResourceList{}
	resourceLimits[v1.ResourceCPU] = resource.MustParse(fmt.Sprintf("%v", task.CpuNumber))
	resourceLimits[v1.ResourceMemory] = resource.MustParse(fmt.Sprintf("%vMi", task.MemoryMB))
	if task.AccDevice != nil {
		// 适配VGPU
		if task.AccDevice.Kind == "vgpu" {
		} else {
			accDeviceWithVendor := ""
			for _, node := range nodes.Items {
				for resourceName := range node.Status.Capacity {
					fmt.Println("/" + task.AccDevice.DeviceType)
					if strings.HasSuffix(string(resourceName), task.AccDevice.DeviceType) {
						accDeviceWithVendor = string(resourceName)
					}
				}
			}

			if len(accDeviceWithVendor) > 0 {
				resourceLimits[v1.ResourceName(accDeviceWithVendor)] = resource.MustParse(fmt.Sprintf("%v", task.AccDevice.Requests))
			}
		}
	}

	return resourceLimits
}

func (h *taskHandler) isJobAllocatable(vcJob *batch.Job, nodes []v1.Node, queue *schedulingv1.Queue, pods []v1.Pod) (bool, error) {
	vcJob.UID = k8stypes.UID(vcJob.Name)

	pg := newPodGroup(vcJob)
	// logs.Debug("IsJobAllocatable: Job <%s/%s>'s PodGroup is %s", vcJob.Namespace, vcJob.Name, jsonutil.ToJSON(pg))

	var vcJobPods []*v1.Pod
	for _, ts := range vcJob.Spec.Tasks {
		ts.Template.Name = ts.Name
		tc := ts.Template.DeepCopy()

		for i := 0; i < int(ts.Replicas); i++ {
			newPod := createJobPod(vcJob, tc, i)
			vcJobPods = append(vcJobPods, newPod)
		}
	}

	binder := &volschutil.FakeBinder{
		Binds:   map[string]string{},
		Channel: make(chan string),
	}

	option := options.ServerOpts
	sc := cache.New(config.NewK8sConfig(), option.SchedulerNames, option.DefaultQueue, option.NodeSelector)
	schedulerCache := sc.(*cache.SchedulerCache)
	schedulerCache.Binder = binder
	schedulerCache.StatusUpdater = &volschutil.FakeStatusUpdater{}
	schedulerCache.VolumeBinder = &volschutil.FakeVolumeBinder{}
	schedulerCache.Recorder = record.NewFakeRecorder(100)

	for index := range nodes {
		if val, found := nodes[index].Labels["resourcepool.system.hero.ai"]; found && val == vcJob.Spec.Queue {
			schedulerCache.AddNode(&nodes[index])
		}
	}
	for index := range pods {
		schedulerCache.AddPod(&pods[index])
	}
	for _, v := range vcJobPods {
		schedulerCache.AddPod(v)
	}
	schedulerCache.AddPodGroupV1beta1(pg)
	schedulerCache.AddQueueV1beta1(queue)

	fakeScheduler, err := NewFakeScheduler(h.schedulerConf)
	if err != nil {
		return false, err
	}
	var allocateAction framework.Action
	for i := range fakeScheduler.Actions {
		if fakeScheduler.Actions[i].Name() == "allocate" {
			allocateAction = fakeScheduler.Actions[i]
			break
		}
	}

	conf.EnabledActionMap = make(map[string]bool)
	for _, action := range fakeScheduler.Actions {
		conf.EnabledActionMap[action.Name()] = true
	}

	ssn := framework.OpenSession(schedulerCache, fakeScheduler.Plugins, fakeScheduler.Configurations)
	defer framework.CloseSession(ssn)
	allocateAction.Execute(ssn)

	// 等待所有 Pods 调度完成
	numPods := 0
	for _, task := range vcJob.Spec.Tasks {
		numPods += int(task.Replicas)
	}
	for {
		finished := false
		select {
		case taskInfo, ok := <-schedulerCache.BindFlowChannel:
			if !ok {
				continue
			}
			binder.Bind(nil, []*api.TaskInfo{taskInfo})
		case <-time.After(2 * time.Second):
			log.Info("IsJobAllocatable scheduling timeout: maybe cannot be scheduled.")
			finished = true
		}
		numPods -= 1
		if numPods <= 0 || finished {
			break
		}
	}

	allocatable := isJobReadyToRun(vcJob, binder.Binds)

	_ = ssn.Jobs[api.JobID(fmt.Sprintf("%s/%s", vcJob.Namespace, vcJob.UID))]
	return allocatable, nil
}

func NewFakeScheduler(schedulerConf *conf.SchedulerConfiguration) (*FakeScheduler, error) {

	actions, plugins, configurations, err := unmarshalSchedulerConf(schedulerConf)
	if err != nil {
		return nil, fmt.Errorf("scheduler config %v is invalid: %v", schedulerConf, err)
	}

	return &FakeScheduler{
		SchedulerConf:  options.ServerOpts.SchedulerConf,
		Actions:        actions,
		Plugins:        plugins,
		Configurations: configurations,
	}, nil
}

func isJobReadyToRun(vcJob *batch.Job, binds map[string]string) bool {
	if len(binds) < int(vcJob.Spec.MinAvailable) {
		return false
	}

	fmt.Println("binds: ", binds)
	fit := make(map[string]int32)
	for _, t := range vcJob.Spec.Tasks {
		for j := int32(0); j < t.Replicas; j++ {
			podName := fmt.Sprintf("%s/%s-%s-%v", vcJob.Namespace, vcJob.Name, t.Name, j)
			if _, found := binds[podName]; found {
				fit[t.Name] += 1
			}
		}
	}
	// fmt.Printf("%#v", fit)
	for _, t := range vcJob.Spec.Tasks {
		if t.MinAvailable != nil {
			n, ok := fit[t.Name]
			if !ok {
				return false
			}
			if n < *t.MinAvailable {
				return false
			}
		}
	}
	return true
}

func newPodGroup(vcJob *batch.Job) *schedulingv1.PodGroup {
	minTaskMember := map[string]int32{}
	for _, task := range vcJob.Spec.Tasks {
		if task.MinAvailable != nil {
			minTaskMember[task.Name] = *task.MinAvailable
		} else {
			minTaskMember[task.Name] = task.Replicas
		}
	}

	pg := &schedulingv1.PodGroup{
		ObjectMeta: metav1.ObjectMeta{
			Namespace:   vcJob.Namespace,
			Name:        vcJob.Name,
			Annotations: vcJob.Annotations,
			Labels:      vcJob.Labels,
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(vcJob, helpers.JobKind),
			},
		},
		Spec: schedulingv1.PodGroupSpec{
			MinMember:         vcJob.Spec.MinAvailable,
			MinTaskMember:     minTaskMember,
			Queue:             vcJob.Spec.Queue,
			MinResources:      calcPGMinResources(vcJob),
			PriorityClassName: vcJob.Spec.PriorityClassName,
		},
		Status: schedulingv1.PodGroupStatus{
			Phase: schedulingv1.PodGroupInqueue,
		},
	}
	return pg
}

func calcPGMinResources(job *batch.Job) *v1.ResourceList {
	minAvailableTasksRes := v1.ResourceList{}
	podCnt := int32(0)
	for _, task := range job.Spec.Tasks {
		for i := int32(0); i < task.Replicas; i++ {
			if podCnt >= job.Spec.MinAvailable {
				break
			}
			podCnt++
			for _, c := range task.Template.Spec.Containers {
				addResourceList(minAvailableTasksRes, c.Resources.Requests, c.Resources.Limits)
			}
		}
	}
	return &minAvailableTasksRes
}

func addResourceList(list, req, limit v1.ResourceList) {
	for name, quantity := range req {
		if value, ok := list[name]; !ok {
			list[name] = quantity.DeepCopy()
		} else {
			value.Add(quantity)
			list[name] = value
		}
	}

	if req != nil {
		return
	}

	// If Requests is omitted for a container,
	// it defaults to Limits if that is explicitly specified.
	for name, quantity := range limit {
		if value, ok := list[name]; !ok {
			list[name] = quantity.DeepCopy()
		} else {
			value.Add(quantity)
			list[name] = value
		}
	}
}

func createJobPod(job *batch.Job, template *v1.PodTemplateSpec, ix int) *v1.Pod {
	templateCopy := template.DeepCopy()

	pod := &v1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      jobhelpers.MakePodName(job.Name, template.Name, ix),
			Namespace: job.Namespace,
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(job, helpers.JobKind),
			},
			Labels:      templateCopy.Labels,
			Annotations: templateCopy.Annotations,
			UID:         k8stypes.UID(jobhelpers.MakePodName(job.Name, template.Name, ix)),
		},
		Spec: templateCopy.Spec,
		Status: v1.PodStatus{
			Phase: v1.PodPending,
		},
	}

	// If no scheduler name in Pod, use scheduler name from Job.
	if len(pod.Spec.SchedulerName) == 0 {
		pod.Spec.SchedulerName = job.Spec.SchedulerName
	}

	tsKey := templateCopy.Name
	if len(tsKey) == 0 {
		tsKey = batch.DefaultTaskSpec
	}

	if len(pod.Annotations) == 0 {
		pod.Annotations = make(map[string]string)
	}

	pod.Annotations[batch.TaskSpecKey] = tsKey
	pod.Annotations[schedulingv1.KubeGroupNameAnnotationKey] = job.Name
	pod.Annotations[batch.JobNameKey] = job.Name
	pod.Annotations[batch.QueueNameKey] = job.Spec.Queue
	pod.Annotations[batch.JobVersion] = fmt.Sprintf("%d", job.Status.Version)
	pod.Annotations[batch.PodTemplateKey] = fmt.Sprintf("%s-%s", job.Name, template.Name)

	if len(job.Annotations) > 0 {
		if value, found := job.Annotations[schedulingv1.PodPreemptable]; found {
			pod.Annotations[schedulingv1.PodPreemptable] = value
		}
		if value, found := job.Annotations[schedulingv1.RevocableZone]; found {
			pod.Annotations[schedulingv1.RevocableZone] = value
		}

		if value, found := job.Annotations[schedulingv1.JDBMinAvailable]; found {
			pod.Annotations[schedulingv1.JDBMinAvailable] = value
		} else if value, found := job.Annotations[schedulingv1.JDBMaxUnavailable]; found {
			pod.Annotations[schedulingv1.JDBMaxUnavailable] = value
		}
	}

	if len(pod.Labels) == 0 {
		pod.Labels = make(map[string]string)
	}

	// Set pod labels for Service.
	pod.Labels[batch.JobNameKey] = job.Name
	pod.Labels[batch.TaskSpecKey] = tsKey
	pod.Labels[batch.JobNamespaceKey] = job.Namespace
	pod.Labels[batch.QueueNameKey] = job.Spec.Queue
	if len(job.Labels) > 0 {
		if value, found := job.Labels[schedulingv1.PodPreemptable]; found {
			pod.Labels[schedulingv1.PodPreemptable] = value
		}
	}

	return pod
}

func unmarshalSchedulerConf(schedulerConf *conf.SchedulerConfiguration) ([]framework.Action, []conf.Tier, []conf.Configuration, error) {
	var actions []framework.Action

	// Set default settings for each plugin if not set
	for i, tier := range schedulerConf.Tiers {
		// drf with hierarchy enabled
		hdrf := false
		// proportion enabled
		proportion := false
		for j := range tier.Plugins {
			if tier.Plugins[j].Name == "drf" &&
				tier.Plugins[j].EnabledHierarchy != nil &&
				*tier.Plugins[j].EnabledHierarchy {
				hdrf = true
			}
			if tier.Plugins[j].Name == "proportion" {
				proportion = true
			}
			plugins.ApplyPluginConfDefaults(&schedulerConf.Tiers[i].Plugins[j])
		}
		if hdrf && proportion {
			return nil, nil, nil, fmt.Errorf("proportion and drf with hierarchy enabled conflicts")
		}
	}

	actionNames := strings.Split(schedulerConf.Actions, ",")
	for _, actionName := range actionNames {
		if action, found := framework.GetAction(strings.TrimSpace(actionName)); found {
			actions = append(actions, action)
		} else {
			return nil, nil, nil, fmt.Errorf("failed to find Action %s, ignore it", actionName)
		}
	}

	return actions, schedulerConf.Tiers, schedulerConf.Configurations, nil
}
