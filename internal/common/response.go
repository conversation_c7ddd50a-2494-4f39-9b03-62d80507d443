//nolint:all
package common

import (
	"hero-apiserver/internal/model"

	"github.com/emicklei/go-restful"
)

type DefaultResponse struct {
	Code   int         `json:"code,omitempty"`
	Msg    string      `json:"msg,omitempty"`
	Reason string      `json:"reason,omitempty"`
	Data   interface{} `json:"metrics_data,omitempty"`
}

func NewDefaultResponse() *DefaultResponse {
	return &DefaultResponse{}
}

func (dr *DefaultResponse) Succ(response *restful.Response, body interface{}) {
	dr.Data = body
	response.WriteEntity(dr)
}

func (dr *DefaultResponse) Fail(response *restful.Response, err error) {
	errResp := response.InternalServerError()
	dr.Code = int(model.QueryFailed)
	dr.Msg = model.ErrMsg(model.ParseReqFailed)
	dr.Reason = err.Error()
	errResp.WriteEntity(dr)
}
