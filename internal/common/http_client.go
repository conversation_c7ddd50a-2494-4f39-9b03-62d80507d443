package common

import (
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"sync"
	"time"

	log "github.com/sirupsen/logrus"
)

const (
	MaxIdleConns          int = 100
	MaxIdleConnsPerHost   int = 100
	ResponseHeaderTimeout int = 20
	IdleConnTimeout       int = 20
)

var httpOnce sync.Once
var transport *http.Transport

type Client interface {
	Get(base string, endpoint string, headers http.Header, basicAuth *BasicAuth, responseStruct interface{}, querystring map[string]string) (*http.Response, error)
	Post(base string, endpoint string, headers http.Header, basicAuth *BasicAuth, payload io.Reader, responseStruct interface{}, querystring map[string]string) (*http.Response, error)
	Do(ar *APIRequest, responseStruct interface{}, options ...interface{}) (*http.Response, error)
}

func NewTransport() *http.Transport {
	httpOnce.Do(func() {
		transport = &http.Transport{
			DialContext: (&net.Dialer{
				Timeout:   time.Duration(IdleConnTimeout) * time.Second,
				KeepAlive: time.Duration(IdleConnTimeout) * time.Second,
			}).DialContext,
			MaxIdleConns:        200,
			MaxIdleConnsPerHost: MaxIdleConnsPerHost,
			IdleConnTimeout:     time.Duration(IdleConnTimeout) * time.Second,
			DisableKeepAlives:   true,
			TLSClientConfig:     &tls.Config{InsecureSkipVerify: true},
		}
	})
	return transport
}

func NewHTTPClient() Client {
	return &HTTPClient{Client: &http.Client{Transport: &http.Transport{
		Dial: (&net.Dialer{
			Timeout:   20 * time.Second,
			KeepAlive: 200 * time.Second,
		}).Dial,
		DisableKeepAlives:     false,
		MaxIdleConns:          MaxIdleConns,
		MaxIdleConnsPerHost:   MaxIdleConnsPerHost,
		IdleConnTimeout:       time.Duration(IdleConnTimeout) * time.Second,
		ResponseHeaderTimeout: time.Duration(ResponseHeaderTimeout) * time.Second,
		TLSClientConfig:       &tls.Config{InsecureSkipVerify: true},
	}}}
}

type APIRequest struct {
	Method    string
	Endpoint  string
	Payload   io.Reader
	Headers   http.Header
	Suffix    string
	Base      string
	BasicAuth *BasicAuth
}

type BasicAuth struct {
	Username string
	Password string
}

type HTTPClient struct {
	Client *http.Client
}

func NewAPIRequest(method, base, endpoint string, headers http.Header, basicAuth *BasicAuth, payload io.Reader) *APIRequest {
	var suffix string
	ar := &APIRequest{method, endpoint, payload, headers, suffix, base, basicAuth}
	return ar
}

func (r *HTTPClient) Post(base string, endpoint string, headers http.Header, basicAuth *BasicAuth, payload io.Reader, responseStruct interface{}, querystring map[string]string) (*http.Response, error) {
	ar := NewAPIRequest(http.MethodPost, base, endpoint, headers, basicAuth, payload)
	ar.Suffix = ""
	return r.Do(ar, responseStruct, querystring)
}

func (r *HTTPClient) Get(base string, endpoint string, headers http.Header, basicAuth *BasicAuth, responseStruct interface{}, querystring map[string]string) (*http.Response, error) {
	ar := NewAPIRequest(http.MethodGet, base, endpoint, headers, basicAuth, nil)
	ar.Suffix = ""
	return r.Do(ar, responseStruct, querystring)
}

func (r *HTTPClient) Do(ar *APIRequest, responseStruct interface{}, options ...interface{}) (*http.Response, error) {

	URL, err := url.Parse(ar.Base + ar.Endpoint + ar.Suffix)
	if err != nil {
		return nil, err
	}

	for _, o := range options {
		switch v := o.(type) {
		case map[string]string:
			querystring := make(url.Values)
			for key, val := range v {
				querystring.Set(key, val)
			}
			URL.RawQuery = querystring.Encode()
		}
	}
	req, err := http.NewRequest(ar.Method, URL.String(), ar.Payload)
	if err != nil {
		return nil, err
	}
	log.Info("===========query==========:", req.URL.RawQuery)
	if ar.BasicAuth != nil {
		req.SetBasicAuth(ar.BasicAuth.Username, ar.BasicAuth.Password)
	}

	if ar.Headers != nil {
		for k := range ar.Headers {
			req.Header.Add(k, ar.Headers.Get(k))
		}
	}
	log.Info("=========== Start Do ==========:", time.Now())
	response, err := r.Client.Do(req)
	if err != nil {
		return nil, err
	}
	log.Info("=========== End Do ==========:", time.Now())
	if response.StatusCode != http.StatusOK {
		defer response.Body.Close()
		return nil, fmt.Errorf("request fail, status:%s, error:%s", response.Status, response.Body)
	}
	switch responseStruct.(type) {
	case *string:
		return r.ReadRawResponse(response, responseStruct)
	default:
		return r.ReadJSONResponse(response, responseStruct)
	}

}
func (r *HTTPClient) ReadRawResponse(response *http.Response, responseStruct interface{}) (*http.Response, error) {
	defer response.Body.Close()

	content, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, err
	}
	if str, ok := responseStruct.(*string); ok {
		*str = string(content)
	} else {
		return nil, errors.New("COUND NOT CAST RESPONSESTRUCT TO *STRING")
	}

	return response, nil
}

func (r *HTTPClient) ReadJSONResponse(response *http.Response, responseStruct interface{}) (*http.Response, error) {
	defer response.Body.Close()
	err := json.NewDecoder(response.Body).Decode(responseStruct)
	if err != nil && err.Error() == "EOF" {
		return response, nil
	}
	return response, nil
}
