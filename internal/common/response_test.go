package common

import (
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/emicklei/go-restful"
	"github.com/stretchr/testify/assert"
)

func TestDefaultResponse_Succ(t *testing.T) {
	rec := httptest.NewRecorder()
	resp := restful.NewResponse(rec)
	resp.SetRequestAccepts("application/json")

	dr := NewDefaultResponse()
	body := map[string]string{"key": "value"}
	dr.Succ(resp, body)

	assert.Equal(t, http.StatusOK, rec.Code)

	assert.Contains(t, rec.Body.String(), "\"key\": \"value\"")
}

func TestDefaultResponse_Fail(t *testing.T) {
	rec := httptest.NewRecorder()
	resp := restful.NewResponse(rec)
	resp.SetRequestAccepts("application/json")

	dr := NewDefaultResponse()
	err := errors.New("test error")
	dr.<PERSON>ail(resp, err)

	assert.Equal(t, http.StatusInternalServerError, rec.Code)
	assert.Contains(t, rec.Body.String(), "\"reason\": \"test error\"")
}
