package common

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"
)

func TestHTTPClient_Get(t *testing.T) {
	h := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodGet {
			t.<PERSON>("Expected 'GET' request, got '%s'", r.Method)
		}
		w.<PERSON><PERSON><PERSON>(http.StatusOK)
		w.Write([]byte(`{"message":"success"}`))
	})
	ts := httptest.NewServer(h)
	defer ts.Close()

	client := NewHTTPClient()
	var response map[string]string
	resp, err := client.Get(ts.URL, "", nil, nil, &response, nil)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	if resp.StatusCode != http.StatusOK {
		t.Fatalf("Expected status 200, got %v", resp.StatusCode)
	}
	if response["message"] != "success" {
		t.Fatalf("Expected response message 'success', got '%s'", response["message"])
	}
}

func TestHTTPClient_Post(t *testing.T) {
	h := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodPost {
			t.Errorf("Expected 'POST' request, got '%s'", r.Method)
		}
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"message":"created"}`))
	})
	ts := httptest.NewServer(h)
	defer ts.Close()

	client := NewHTTPClient()
	var response map[string]string
	payload := map[string]string{"name": "test"}
	payloadBytes, _ := json.Marshal(payload)
	resp, err := client.Post(ts.URL, "", nil, nil, bytes.NewReader(payloadBytes), &response, nil)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	if resp.StatusCode != http.StatusOK {
		t.Fatalf("Expected status 200, got %v", resp.StatusCode)
	}
	if response["message"] != "created" {
		t.Fatalf("Expected response message 'created', got '%s'", response["message"])
	}
}

func TestNewTransport(t *testing.T) {
	transport := NewTransport()
	if transport == nil {
		t.Error("Expected non-nil transport")
	}
	if transport.MaxIdleConns != 200 {
		t.Errorf("Expected MaxIdleConns to be 200, but got %d", transport.MaxIdleConns)
	}
	if transport.TLSClientConfig == nil || transport.TLSClientConfig.InsecureSkipVerify != true {
		t.Error("Expected TLSClientConfig to have InsecureSkipVerify set to true")
	}
}

func TestNewAPIRequest(t *testing.T) {
	method := http.MethodGet
	base := "http://example.com"
	endpoint := "/api"
	headers := http.Header{}
	headers.Set("Content-Type", "application/json")
	basicAuth := &BasicAuth{Username: "user", Password: "pass"}
	payload := bytes.NewReader([]byte(`{"key": "value"}`))

	ar := NewAPIRequest(method, base, endpoint, headers, basicAuth, payload)

	if ar.Method != method {
		t.Errorf("Expected method %s, got %s", method, ar.Method)
	}
	if ar.Base != base {
		t.Errorf("Expected base %s, got %s", base, ar.Base)
	}
	if ar.Endpoint != endpoint {
		t.Errorf("Expected endpoint %s, got %s", endpoint, ar.Endpoint)
	}
	if !reflect.DeepEqual(ar.Headers, headers) {
		t.Error("Expected headers to match")
	}
	if ar.BasicAuth.Username != basicAuth.Username || ar.BasicAuth.Password != basicAuth.Password {
		t.Error("Expected basicAuth to match")
	}
	if ar.Payload != payload {
		t.Error("Expected payload to match")
	}
}
