package cmd

import (
	"hero-apiserver/internal/config"
	"hero-apiserver/internal/server"
	"hero-apiserver/internal/utils"
	"os"
	"os/signal"
	"syscall"

	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

type taskContext struct {
	config *config.ServerConfig
	logger *logrus.Entry
	s      *server.APIServer
}

func run(cmd *cobra.Command, args []string) error {
	// 启动任务
	tasks := []func(*taskContext) error{
		setupLog,
		printStartupLog,
		setupServer,
		serve,
	}
	taskCtx := &taskContext{
		config: config.SC,
	}

	for _, t := range tasks {
		if err := t(taskCtx); err != nil {
			taskCtx.logger.Fatalf("%s: %s", utils.GetFunctionName(t), err)
		}
	}

	sigChan := make(chan os.Signal, 1)
	exitChan := make(chan struct{})
	signal.Notify(sig<PERSON>han, os.Interrupt, syscall.SIGTERM)
	// 首次收到停止信息，打印日志
	taskCtx.logger.Info("signal: ", <-sigChan, " signal received")
	// 触发graceful shutdown
	go func() {
		taskCtx.logger.Warning("stopping auth-adapter")
		if err := taskCtx.s.Close(); err != nil {
			taskCtx.logger.Error("taskCtx.s.Close:", err)
		}
		exitChan <- struct{}{}
	}()

	// select操作等待强制终止或主动退出
	select {
	case <-exitChan:
	case s := <-sigChan:
		taskCtx.logger.Info("signal: ", s, " signal received, stopping immediately")
	}
	taskCtx.logger.Info("hero-apiserver stopped")
	return nil
}

func setupLog(taskCtx *taskContext) error {
	logrus.SetLevel(logrus.Level(taskCtx.config.LogLevel))
	taskCtx.logger = logrus.WithField("module", "main")
	taskCtx.logger.Info("setupLog success")
	return nil
}

func printStartupLog(taskCtx *taskContext) error {
	taskCtx.logger.Infof("starting hero-apiserver version: %s configfile: %s", taskCtx.config.Version, viper.ConfigFileUsed())
	return nil
}

func setupServer(taskCtx *taskContext) error {
	instance, err := server.Setup(
		taskCtx.config.HTTP.GetAddress(),
		config.NewK8sClient(),
		config.NewKarmadaClient(),
		config.NewVolcanoClient(),
		config.NewDynamicClient(),
		config.NewMinioS3Client(),
		config.NewMinioFSClient(),
		config.NewMqClient(),
		&config.SC.VolcanoCf,
		config.NewSSHCongfig(),
	)
	if err != nil {
		return errors.Wrap(err, "server.Setup")
	}
	config.InitVolcanoSchedulerOptions()
	taskCtx.s = instance

	taskCtx.logger.Info("setup server success")
	return nil
}

func serve(taskCtx *taskContext) error {
	if err := taskCtx.s.Serve(taskCtx.config.TLS); err != nil {
		return errors.Wrap(err, "taskCtx.s.Serve")
	}
	return nil
}
