//nolint:all
package cmd

import (
	"fmt"
	"os"

	"hero-apiserver/internal/config"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

var version string
var cfgFile string

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "hero-apiserver",
	Short: "hero-apiserver cmdline tool",
	Long:  `An amagzing cmdline tool that handles stats of hero`,
	RunE:  run,
}

func Execute(v string) {
	version = v
	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}

func init() {
	cobra.OnInitialize(initConfig)
	initFlags()
	initCmds()
}

// initConfig reads in config file and ENV variables if set.
func initConfig() {
	config.SC.Version = version
}

func initFlags() {
	// Here you will define your flags and configuration settings.
	// Cobra supports persistent flags, which, if defined here,
	// will be global for your application.

	rootCmd.PersistentFlags().StringVarP(&cfgFile, "config", "c", "", "config file (default is /etc/hero-apiserver/hero_apiserver.yaml)")
	// Bind cmdline param log-level with config param log.level
	rootCmd.PersistentFlags().Int("log-level", 4, "debug=5, info=4, warn=3, error=2, fatal=1, panic=0")
	viper.BindPFlag("logLevel", rootCmd.PersistentFlags().Lookup("log-level"))
	// HTTP Service
	viper.SetDefault("http.addr", "127.0.0.1")
	viper.SetDefault("http.port", 8080)
	viper.SetDefault("http.tls", true)
}

func initCmds() {
	rootCmd.AddCommand(versionCmd)
	rootCmd.AddCommand(configCmd)
}
