package cmd

import (
	"os"
	"text/template"

	"github.com/pkg/errors"
	"github.com/spf13/cobra"

	"hero-apiserver/internal/config"
)

const configTemplate = `{
	"http": {
		"addr": "{{.server.Addr}}",
		"port": {{.server.Port}},
		"tls": {{.server.tls}}
	},
	"logLevel": {{.logLevel}}
}
`

var configCmd = &cobra.Command{
	Use:   "configfile",
	Short: "Print hero-apiserver configuration file",
	RunE: func(cmd *cobra.Command, args []string) error {
		t := template.Must(template.New("config").Parse(configTemplate))
		err := t.Execute(os.Stdout, &config.SC)
		if err != nil {
			return errors.Wrap(err, "execute config template")
		}
		return nil
	},
}
